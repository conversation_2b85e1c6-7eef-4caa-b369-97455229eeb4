// Static hotel showcase component that works without Firebase
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Star, 
  MapPin, 
  Phone, 
  Mail, 
  Wifi, 
  Car, 
  Utensils, 
  Dumbbell,
  Waves,
  Coffee,
  Shield,
  Clock,
  Users,
  IndianRupee,
  Heart,
  Share2,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';

interface StaticHotel {
  id: string;
  name: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  rating: number;
  reviews: number;
  priceRange: {
    min: number;
    max: number;
  };
  images: string[];
  amenities: string[];
  roomTypes: string[];
  isPopular?: boolean;
  distanceFromTemple: string;
}

interface StaticHotelShowcaseProps {
  className?: string;
  onSelectHotel?: (hotelId: string) => void;
}

const StaticHotelShowcase: React.FC<StaticHotelShowcaseProps> = ({ className, onSelectHotel }) => {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Static hotel data for Tiruvannamalai
  const hotels: StaticHotel[] = [
    {
      id: 'hotel-1',
      name: 'Arunachala Palace Hotel',
      description: 'Luxury heritage hotel with traditional Tamil architecture and modern amenities, offering breathtaking views of the sacred Arunachala mountain.',
      address: 'Temple Street, Tiruvannamalai, Tamil Nadu 606601',
      phone: '+91 4175 234567',
      email: '<EMAIL>',
      website: 'www.arunachalapalace.com',
      rating: 4.8,
      reviews: 324,
      priceRange: { min: 4500, max: 12000 },
      images: [
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png'
      ],
      amenities: ['Free WiFi', 'Restaurant', 'Parking', 'AC', 'Room Service', 'Temple View'],
      roomTypes: ['Heritage Suite', 'Deluxe Room', 'Family Suite'],
      isPopular: true,
      distanceFromTemple: '0.2 km'
    },
    {
      id: 'hotel-2',
      name: 'Ramana Ashram Guest House',
      description: 'Peaceful spiritual retreat with meditation facilities and serene garden views, perfect for spiritual seekers.',
      address: 'Ramana Nagar, Tiruvannamalai, Tamil Nadu 606603',
      phone: '+91 4175 234568',
      email: '<EMAIL>',
      website: 'www.ramanashram.org',
      rating: 4.6,
      reviews: 189,
      priceRange: { min: 2800, max: 6500 },
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png'
      ],
      amenities: ['Free WiFi', 'Meditation Hall', 'Garden', 'Library', 'Organic Food'],
      roomTypes: ['Spiritual Retreat Room', 'Meditation Suite', 'Simple Room'],
      distanceFromTemple: '1.5 km'
    },
    {
      id: 'hotel-3',
      name: 'Tiruvannamalai Business Hotel',
      description: 'Modern business hotel with conference facilities and executive amenities for corporate travelers.',
      address: 'Gandhi Road, Tiruvannamalai, Tamil Nadu 606601',
      phone: '+91 4175 234569',
      email: '<EMAIL>',
      website: 'www.tvmbusinesshotel.com',
      rating: 4.4,
      reviews: 267,
      priceRange: { min: 3500, max: 8500 },
      images: [
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png',
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png'
      ],
      amenities: ['Free WiFi', 'Business Center', 'Conference Room', 'Gym', 'Restaurant'],
      roomTypes: ['Executive Room', 'Business Suite', 'Standard Room'],
      distanceFromTemple: '0.8 km'
    },
    {
      id: 'hotel-4',
      name: 'Family Paradise Resort',
      description: 'Family-friendly resort with spacious accommodations and recreational facilities for all ages.',
      address: 'Chengam Road, Tiruvannamalai, Tamil Nadu 606601',
      phone: '+91 4175 234570',
      email: '<EMAIL>',
      website: 'www.familyparadiseresort.com',
      rating: 4.7,
      reviews: 412,
      priceRange: { min: 4200, max: 9800 },
      images: [
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png'
      ],
      amenities: ['Swimming Pool', 'Kids Play Area', 'Restaurant', 'Parking', 'Garden'],
      roomTypes: ['Family Suite', 'Deluxe Family Room', 'Connecting Rooms'],
      isPopular: true,
      distanceFromTemple: '2.1 km'
    },
    {
      id: 'hotel-5',
      name: 'Pilgrim\'s Rest Lodge',
      description: 'Budget-friendly accommodation with essential amenities for pilgrims and budget travelers.',
      address: 'Car Street, Tiruvannamalai, Tamil Nadu 606601',
      phone: '+91 4175 234571',
      email: '<EMAIL>',
      website: 'www.pilgrimsrestlodge.com',
      rating: 4.2,
      reviews: 156,
      priceRange: { min: 1800, max: 4200 },
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png'
      ],
      amenities: ['Free WiFi', 'AC', 'Hot Water', 'Temple Shuttle', 'Basic Restaurant'],
      roomTypes: ['Budget Room', 'Standard Room', 'Dormitory'],
      distanceFromTemple: '0.1 km'
    }
  ];

  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi')) return <Wifi className="w-4 h-4" />;
    if (amenityLower.includes('restaurant') || amenityLower.includes('food')) return <Utensils className="w-4 h-4" />;
    if (amenityLower.includes('parking') || amenityLower.includes('car')) return <Car className="w-4 h-4" />;
    if (amenityLower.includes('gym') || amenityLower.includes('fitness')) return <Dumbbell className="w-4 h-4" />;
    if (amenityLower.includes('pool') || amenityLower.includes('swimming')) return <Waves className="w-4 h-4" />;
    if (amenityLower.includes('coffee') || amenityLower.includes('cafe')) return <Coffee className="w-4 h-4" />;
    if (amenityLower.includes('security') || amenityLower.includes('safe')) return <Shield className="w-4 h-4" />;
    return <Star className="w-4 h-4" />;
  };

  const toggleFavorite = (hotelId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(hotelId)) {
        newFavorites.delete(hotelId);
        toast.success('Removed from favorites');
      } else {
        newFavorites.add(hotelId);
        toast.success('Added to favorites');
      }
      return newFavorites;
    });
  };

  const handleSelectHotel = (hotel: StaticHotel) => {
    if (onSelectHotel) {
      onSelectHotel(hotel.id);
    }
    // Scroll to booking section
    const bookingSection = document.getElementById('booking');
    if (bookingSection) {
      bookingSection.scrollIntoView({ behavior: 'smooth' });
      toast.success(`Selected ${hotel.name}. Please complete the booking form.`);
    }
  };

  return (
    <section id="hotels" className={cn('py-16 md:py-24 bg-gradient-to-br from-blue-50 to-indigo-50', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Premium Hotels in Tiruvannamalai
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover our carefully selected hotels near the sacred Arunachaleswarar Temple, 
              offering comfort and spirituality in the heart of Tamil Nadu.
            </p>
          </div>
        </FadeIn>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {hotels.map((hotel, index) => (
            <FadeIn key={hotel.id} delay={200 + index * 100}>
              <Card className="overflow-hidden hover:shadow-xl transition-all duration-500 transform hover:scale-105 bg-white/90 backdrop-blur-sm">
                {/* Hotel Image */}
                <div className="relative h-56 overflow-hidden">
                  <img 
                    src={hotel.images[0]} 
                    alt={hotel.name}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                    onError={(e) => {
                      e.currentTarget.src = '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png';
                    }}
                  />
                  
                  {/* Image Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  
                  {/* Popular Badge */}
                  {hotel.isPopular && (
                    <Badge className="absolute top-4 left-4 bg-orange-500 text-white flex items-center gap-1">
                      <Star className="w-3 h-3 fill-white" />
                      Popular
                    </Badge>
                  )}

                  {/* Favorite Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm hover:bg-white/30"
                    onClick={() => toggleFavorite(hotel.id)}
                  >
                    <Heart 
                      className={cn(
                        "w-5 h-5",
                        favorites.has(hotel.id) ? "fill-red-500 text-red-500" : "text-white"
                      )} 
                    />
                  </Button>

                  {/* Price Range */}
                  <div className="absolute bottom-4 right-4">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                      <div className="text-lg font-bold text-primary">
                        ₹{hotel.priceRange.min.toLocaleString()} - ₹{hotel.priceRange.max.toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">per night</div>
                    </div>
                  </div>

                  {/* Distance */}
                  <div className="absolute bottom-4 left-4">
                    <Badge variant="secondary" className="bg-white/90 backdrop-blur-sm">
                      <MapPin className="w-3 h-3 mr-1" />
                      {hotel.distanceFromTemple}
                    </Badge>
                  </div>
                </div>

                {/* Hotel Details */}
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Hotel Header */}
                    <div>
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="text-xl font-semibold line-clamp-1">{hotel.name}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex items-center gap-1">
                              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                              <span className="font-medium text-sm">{hotel.rating}</span>
                            </div>
                            <span className="text-sm text-muted-foreground">
                              ({hotel.reviews} reviews)
                            </span>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="flex-shrink-0"
                          onClick={() => toast.info('Share feature coming soon!')}
                        >
                          <Share2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Hotel Description */}
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {hotel.description}
                    </p>

                    {/* Contact Info */}
                    <div className="grid grid-cols-1 gap-2 text-sm">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <span className="line-clamp-1">{hotel.address}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-muted-foreground" />
                        <span>{hotel.phone}</span>
                      </div>
                    </div>

                    {/* Room Types */}
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium">Room Types:</span>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {hotel.roomTypes.slice(0, 3).map((type, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Amenities */}
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium">Amenities:</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {hotel.amenities.slice(0, 4).map((amenity, idx) => (
                          <div key={idx} className="flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-full">
                            {getAmenityIcon(amenity)}
                            <span>{amenity}</span>
                          </div>
                        ))}
                        {hotel.amenities.length > 4 && (
                          <div className="flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-full">
                            <span>+{hotel.amenities.length - 4} more</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      <Button 
                        className="flex-1 btn-primary"
                        onClick={() => handleSelectHotel(hotel)}
                      >
                        <IndianRupee className="w-4 h-4" />
                        Book Now
                      </Button>
                      <Button 
                        variant="outline"
                        size="icon"
                        onClick={() => toast.info('Hotel details feature coming soon!')}
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </FadeIn>
          ))}
        </div>

        {/* Bottom CTA */}
        <FadeIn delay={400}>
          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              Need help choosing the perfect hotel for your spiritual journey?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" className="btn-outline">
                <Phone className="w-4 h-4 mr-2" />
                Call: +91 4175 234567
              </Button>
              <Button className="btn-primary">
                <Mail className="w-4 h-4 mr-2" />
                Email: <EMAIL>
              </Button>
            </div>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default StaticHotelShowcase;
