// Firebase configuration and initialization
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Firebase configuration
// Replace these with your actual Firebase project configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyB-6gHW8iieLcmHnlG-K36g0VpUZpFBgGA",
  authDomain: "one-touch-hotel-booking.firebaseapp.com",
  databaseURL: "https://one-touch-hotel-booking-default-rtdb.firebaseio.com",
  projectId: "one-touch-hotel-booking",
  storageBucket: "one-touch-hotel-booking.firebasestorage.app",
  messagingSenderId: "716354145184",
  appId: "1:716354145184:web:e6ba7acdea3f14dae632c5",
  measurementId: "G-KJ14H9ZTV0"
};
// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

export default app;
