/* Enhanced Amenities Scrolling Animation Styles */

/* Hide scrollbars for all browsers */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Smooth scrolling animation */
.amenities-scroll-container {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Enhanced card hover effects */
.amenity-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.amenity-card:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Icon animation */
.amenity-icon {
  transition: all 0.3s ease;
}

.amenity-card:hover .amenity-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Gradient overlays for smooth edges */
.scroll-gradient-left {
  background: linear-gradient(90deg, 
    hsl(var(--muted) / 0.3) 0%, 
    hsl(var(--muted) / 0.2) 50%, 
    transparent 100%
  );
}

.scroll-gradient-right {
  background: linear-gradient(270deg, 
    hsl(var(--muted) / 0.3) 0%, 
    hsl(var(--muted) / 0.2) 50%, 
    transparent 100%
  );
}

/* Enhanced card styling */
.amenity-card-enhanced {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Icon container styling */
.amenity-icon-container {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.2) 0%, 
    hsl(var(--primary) / 0.1) 100%
  );
  transition: all 0.3s ease;
}

.amenity-card:hover .amenity-icon-container {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.3) 0%, 
    hsl(var(--primary) / 0.2) 100%
  );
  transform: scale(1.1);
}

/* Text animations */
.amenity-title {
  transition: color 0.3s ease;
}

.amenity-card:hover .amenity-title {
  color: hsl(var(--primary));
}

.amenity-description {
  transition: all 0.3s ease;
}

.amenity-card:hover .amenity-description {
  color: hsl(var(--foreground) / 0.8);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .amenity-card {
    width: 240px;
  }
  
  .amenity-card:hover {
    transform: translateY(-4px) scale(1.02);
  }
}

@media (max-width: 480px) {
  .amenity-card {
    width: 200px;
  }
  
  .scroll-gradient-left,
  .scroll-gradient-right {
    width: 15px;
  }
}

/* Animation keyframes for enhanced effects */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

/* Special effects for featured amenities */
.amenity-featured {
  animation: pulse-glow 3s ease-in-out infinite;
}

.amenity-floating {
  animation: float 6s ease-in-out infinite;
}

/* Smooth scroll behavior */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Loading animation for amenities */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.amenity-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Enhanced accessibility */
.amenity-card:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

.amenity-card:focus-visible {
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.5);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .amenity-card {
    border: 2px solid currentColor;
  }
  
  .amenity-icon-container {
    border: 1px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .amenity-card,
  .amenity-icon,
  .amenity-icon-container,
  .amenity-title,
  .amenity-description {
    transition: none !important;
    animation: none !important;
  }
  
  .amenity-card:hover {
    transform: none !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .amenity-card-enhanced {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .scroll-gradient-left {
    background: linear-gradient(90deg, 
      rgba(0, 0, 0, 0.3) 0%, 
      rgba(0, 0, 0, 0.2) 50%, 
      transparent 100%
    );
  }
  
  .scroll-gradient-right {
    background: linear-gradient(270deg, 
      rgba(0, 0, 0, 0.3) 0%, 
      rgba(0, 0, 0, 0.2) 50%, 
      transparent 100%
    );
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .amenity-card:hover {
    transform: none;
  }
  
  .amenity-card:active {
    transform: scale(0.98);
  }
}

/* Print styles */
@media print {
  .scroll-gradient-left,
  .scroll-gradient-right {
    display: none;
  }
  
  .amenity-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

/* Performance optimizations */
.amenities-scroll-container {
  will-change: scroll-position;
  contain: layout style paint;
}

.amenity-card {
  will-change: transform;
  contain: layout style paint;
}

/* Custom scrollbar for desktop (optional) */
@media (min-width: 1024px) {
  .amenities-scroll-container::-webkit-scrollbar {
    height: 4px;
  }
  
  .amenities-scroll-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }
  
  .amenities-scroll-container::-webkit-scrollbar-thumb {
    background: hsl(var(--primary) / 0.5);
    border-radius: 2px;
  }
  
  .amenities-scroll-container::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.7);
  }
}
