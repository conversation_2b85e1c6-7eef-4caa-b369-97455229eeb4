// Static rooms component with predefined room data
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Calendar, 
  Users, 
  Wifi, 
  Coffee, 
  Car, 
  Tv, 
  Bed,
  Bath,
  Wind,
  Utensils,
  Shield,
  Sparkles,
  Phone,
  IndianRupee,
  Star,
  MapPin,
  Heart,
  Share2
} from 'lucide-react';
import { toast } from 'sonner';

interface StaticRoom {
  id: string;
  name: string;
  type: string;
  price: number;
  originalPrice?: number;
  capacity: number;
  size: string;
  bedType: string;
  images: string[];
  amenities: string[];
  description: string;
  rating: number;
  reviews: number;
  available: number;
  total: number;
  hotelName: string;
  isPopular?: boolean;
  discount?: number;
}

interface StaticRoomsProps {
  className?: string;
  onBookRoom?: (roomId: string) => void;
}

const StaticRooms: React.FC<StaticRoomsProps> = ({ className, onBookRoom }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Static room data - this will always work without Firebase
  const rooms: StaticRoom[] = [
    {
      id: '1',
      name: 'Arunachala Heritage Suite',
      type: 'Heritage Suite',
      price: 8500,
      originalPrice: 10000,
      capacity: 4,
      size: '75 sqm',
      bedType: 'King + Sofa Bed',
      images: [
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png'
      ],
      amenities: ['Free WiFi', 'AC', 'Mini Bar', 'Balcony', 'Temple View', 'Room Service'],
      description: 'Luxurious heritage suite with traditional Tamil architecture and modern amenities, offering breathtaking views of the sacred Arunachala mountain.',
      rating: 4.8,
      reviews: 124,
      available: 3,
      total: 5,
      hotelName: 'Arunachala Palace Hotel',
      isPopular: true,
      discount: 15
    },
    {
      id: '2',
      name: 'Spiritual Retreat Room',
      type: 'Deluxe Room',
      price: 4200,
      capacity: 2,
      size: '45 sqm',
      bedType: 'Queen Size Bed',
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png'
      ],
      amenities: ['Free WiFi', 'AC', 'Meditation Corner', 'Garden View', 'Organic Toiletries'],
      description: 'Peaceful room designed for spiritual seekers with meditation facilities and serene garden views.',
      rating: 4.6,
      reviews: 89,
      available: 8,
      total: 12,
      hotelName: 'Ramana Ashram Guest House'
    },
    {
      id: '3',
      name: 'Business Executive Room',
      type: 'Executive Room',
      price: 5500,
      capacity: 2,
      size: '50 sqm',
      bedType: 'King Size Bed',
      images: [
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png',
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png'
      ],
      amenities: ['Free WiFi', 'AC', 'Work Desk', 'Business Center Access', 'City View'],
      description: 'Modern executive room perfect for business travelers with all necessary amenities.',
      rating: 4.4,
      reviews: 156,
      available: 6,
      total: 10,
      hotelName: 'Tiruvannamalai Business Hotel'
    },
    {
      id: '4',
      name: 'Family Comfort Suite',
      type: 'Family Suite',
      price: 7200,
      capacity: 6,
      size: '85 sqm',
      bedType: 'King + Twin Beds',
      images: [
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png'
      ],
      amenities: ['Free WiFi', 'AC', 'Kitchenette', 'Two Bathrooms', 'Kids Area'],
      description: 'Spacious family suite with separate areas for adults and children, perfect for family pilgrimages.',
      rating: 4.7,
      reviews: 203,
      available: 4,
      total: 8,
      hotelName: 'Family Paradise Resort'
    },
    {
      id: '5',
      name: 'Budget Comfort Room',
      type: 'Standard Room',
      price: 2800,
      capacity: 2,
      size: '30 sqm',
      bedType: 'Double Bed',
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png'
      ],
      amenities: ['Free WiFi', 'AC', 'TV', 'Hot Water', 'Daily Housekeeping'],
      description: 'Comfortable and affordable room with essential amenities for budget-conscious travelers.',
      rating: 4.2,
      reviews: 312,
      available: 12,
      total: 15,
      hotelName: 'Pilgrim\'s Rest Lodge'
    },
    {
      id: '6',
      name: 'Honeymoon Villa',
      type: 'Villa',
      price: 12000,
      originalPrice: 15000,
      capacity: 2,
      size: '100 sqm',
      bedType: 'King Size Bed',
      images: [
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png'
      ],
      amenities: ['Private Pool', 'Jacuzzi', 'Butler Service', 'Champagne', 'Flower Decoration'],
      description: 'Romantic villa with private amenities perfect for couples seeking luxury and privacy.',
      rating: 4.9,
      reviews: 67,
      available: 2,
      total: 3,
      hotelName: 'Romantic Retreat Villa',
      discount: 20
    },
    {
      id: '7',
      name: 'Yoga Retreat Room',
      type: 'Wellness Room',
      price: 3800,
      capacity: 2,
      size: '40 sqm',
      bedType: 'Twin Beds',
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png'
      ],
      amenities: ['Yoga Mat', 'Meditation Cushions', 'Herbal Tea', 'Aromatherapy', 'Garden Access'],
      description: 'Wellness-focused room with yoga and meditation facilities in a tranquil setting.',
      rating: 4.5,
      reviews: 145,
      available: 7,
      total: 10,
      hotelName: 'Wellness Sanctuary'
    },
    {
      id: '8',
      name: 'Temple View Deluxe',
      type: 'Deluxe Room',
      price: 4800,
      capacity: 3,
      size: '48 sqm',
      bedType: 'King + Single Bed',
      images: [
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png'
      ],
      amenities: ['Temple View', 'Prayer Corner', 'Religious Books', 'Incense', 'Spiritual Guidance'],
      description: 'Room with direct temple view and spiritual amenities for the devout pilgrim.',
      rating: 4.6,
      reviews: 198,
      available: 5,
      total: 8,
      hotelName: 'Sacred View Hotel',
      isPopular: true
    },
    {
      id: '9',
      name: 'Luxury Presidential Suite',
      type: 'Presidential Suite',
      price: 18000,
      originalPrice: 22000,
      capacity: 8,
      size: '150 sqm',
      bedType: 'King + Queen + Sofa Beds',
      images: [
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png'
      ],
      amenities: ['Private Terrace', 'Personal Chef', 'Concierge', 'Limousine Service', 'Premium Bar'],
      description: 'Ultimate luxury suite with personalized services and premium amenities for VIP guests.',
      rating: 5.0,
      reviews: 34,
      available: 1,
      total: 2,
      hotelName: 'Grand Palace Tiruvannamalai',
      discount: 18
    },
    {
      id: '10',
      name: 'Garden Cottage',
      type: 'Cottage',
      price: 6200,
      capacity: 4,
      size: '60 sqm',
      bedType: 'Queen + Twin Beds',
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png'
      ],
      amenities: ['Private Garden', 'Outdoor Seating', 'BBQ Facility', 'Nature View', 'Bird Watching'],
      description: 'Charming cottage surrounded by lush gardens and nature, perfect for nature lovers.',
      rating: 4.7,
      reviews: 178,
      available: 6,
      total: 8,
      hotelName: 'Nature\'s Haven Resort'
    }
  ];

  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi')) return <Wifi className="w-4 h-4" />;
    if (amenityLower.includes('ac') || amenityLower.includes('air')) return <Wind className="w-4 h-4" />;
    if (amenityLower.includes('tv')) return <Tv className="w-4 h-4" />;
    if (amenityLower.includes('bath')) return <Bath className="w-4 h-4" />;
    if (amenityLower.includes('car') || amenityLower.includes('parking')) return <Car className="w-4 h-4" />;
    if (amenityLower.includes('restaurant') || amenityLower.includes('dining')) return <Utensils className="w-4 h-4" />;
    if (amenityLower.includes('coffee') || amenityLower.includes('tea')) return <Coffee className="w-4 h-4" />;
    if (amenityLower.includes('security') || amenityLower.includes('safe')) return <Shield className="w-4 h-4" />;
    return <Sparkles className="w-4 h-4" />;
  };

  const toggleFavorite = (roomId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(roomId)) {
        newFavorites.delete(roomId);
        toast.success('Removed from favorites');
      } else {
        newFavorites.add(roomId);
        toast.success('Added to favorites');
      }
      return newFavorites;
    });
  };

  const handleBookNow = (room: StaticRoom) => {
    if (onBookRoom) {
      onBookRoom(room.id);
    } else {
      // Scroll to booking section
      const bookingSection = document.getElementById('booking');
      if (bookingSection) {
        bookingSection.scrollIntoView({ behavior: 'smooth' });
        toast.success(`Selected ${room.name}. Please complete the booking form below.`);
      } else {
        // Navigate to touch booking page
        window.location.href = '/touch-booking';
      }
    }
  };

  const categories = ['all', 'heritage', 'deluxe', 'family', 'business', 'wellness', 'luxury'];
  
  const filteredRooms = selectedCategory === 'all' 
    ? rooms 
    : rooms.filter(room => room.type.toLowerCase().includes(selectedCategory));

  return (
    <section id="rooms" className={cn('py-16 md:py-24 bg-gradient-to-br from-blue-50 to-indigo-50', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Premium Rooms & Suites in Tiruvannamalai
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover comfort and luxury in the spiritual heart of Tamil Nadu. Choose from our carefully selected accommodations.
            </p>
          </div>
        </FadeIn>

        {/* Category Filter */}
        <FadeIn delay={100}>
          <div className="flex justify-center mb-8">
            <div className="w-full max-w-md">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Rooms</SelectItem>
                  <SelectItem value="heritage">Heritage</SelectItem>
                  <SelectItem value="deluxe">Deluxe</SelectItem>
                  <SelectItem value="family">Family</SelectItem>
                  <SelectItem value="business">Business</SelectItem>
                  <SelectItem value="wellness">Wellness</SelectItem>
                  <SelectItem value="luxury">Luxury</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </FadeIn>

        {/* Room Cards Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRooms.map((room, index) => (
            <FadeIn key={room.id} delay={200 + index * 100}>
              <Card className="overflow-hidden hover:shadow-xl transition-all duration-500 transform hover:scale-105 bg-white/90 backdrop-blur-sm">
                {/* Room Image */}
                <div className="relative h-56 overflow-hidden">
                  <img
                    src={room.images[0]}
                    alt={room.name}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                    onError={(e) => {
                      e.currentTarget.src = '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png';
                    }}
                  />

                  {/* Image Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

                  {/* Top Badges */}
                  <div className="absolute top-4 left-4 flex gap-2">
                    {room.isPopular && (
                      <Badge className="bg-orange-500 text-white flex items-center gap-1">
                        <Star className="w-3 h-3 fill-white" />
                        Popular
                      </Badge>
                    )}
                    {room.discount && (
                      <Badge className="bg-red-500 text-white">
                        {room.discount}% OFF
                      </Badge>
                    )}
                  </div>

                  {/* Favorite Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm hover:bg-white/30"
                    onClick={() => toggleFavorite(room.id)}
                  >
                    <Heart
                      className={cn(
                        "w-5 h-5",
                        favorites.has(room.id) ? "fill-red-500 text-red-500" : "text-white"
                      )}
                    />
                  </Button>

                  {/* Price Badge */}
                  <div className="absolute bottom-4 right-4">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                      {room.originalPrice && (
                        <div className="text-xs text-muted-foreground line-through">
                          ₹{room.originalPrice.toLocaleString()}
                        </div>
                      )}
                      <div className="text-lg font-bold text-primary">
                        ₹{room.price.toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">per night</div>
                    </div>
                  </div>

                  {/* Hotel Name */}
                  <div className="absolute bottom-4 left-4">
                    <Badge variant="secondary" className="bg-white/90 backdrop-blur-sm">
                      {room.hotelName}
                    </Badge>
                  </div>
                </div>

                {/* Room Details */}
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Room Header */}
                    <div>
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="text-xl font-semibold line-clamp-1">{room.name}</h3>
                          <Badge variant="outline" className="mt-1 capitalize">
                            {room.type}
                          </Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="flex-shrink-0"
                          onClick={() => toast.info('Share feature coming soon!')}
                        >
                          <Share2 className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="flex items-center gap-2 mb-3">
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                          <span className="font-medium text-sm">{room.rating}</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          ({room.reviews} reviews)
                        </span>
                      </div>
                    </div>

                    {/* Room Info */}
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-muted-foreground" />
                        <span>{room.capacity} guests</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <span>{room.size}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Bed className="w-4 h-4 text-muted-foreground" />
                        <span>{room.bedType}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-muted-foreground" />
                        <span>{room.available}/{room.total} available</span>
                      </div>
                    </div>

                    {/* Room Description */}
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {room.description}
                    </p>

                    {/* Amenities */}
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium">Amenities:</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {room.amenities.slice(0, 4).map((amenity, idx) => (
                          <div key={idx} className="flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-full">
                            {getAmenityIcon(amenity)}
                            <span>{amenity}</span>
                          </div>
                        ))}
                        {room.amenities.length > 4 && (
                          <div className="flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-full">
                            <span>+{room.amenities.length - 4} more</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Availability Status */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {room.available > 0 ? (
                          <div className="flex items-center gap-1 text-green-600">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="text-sm font-medium">Available</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 text-red-600">
                            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            <span className="text-sm font-medium">Sold Out</span>
                          </div>
                        )}
                      </div>

                      {room.available <= 3 && room.available > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          Only {room.available} left!
                        </Badge>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        className="flex-1"
                        onClick={() => handleBookNow(room)}
                        disabled={room.available === 0}
                      >
                        {room.available === 0 ? 'Sold Out' : 'Book Now'}
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => toast.info('Room details feature coming soon!')}
                      >
                        <MapPin className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </FadeIn>
          ))}
        </div>

        {/* Bottom CTA */}
        <FadeIn delay={400}>
          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              Need help choosing the perfect room for your spiritual journey?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline">
                <Phone className="w-4 h-4 mr-2" />
                Call: +91 4175 234567
              </Button>
              <Button onClick={() => window.location.href = '/touch-booking'}>
                <Calendar className="w-4 h-4 mr-2" />
                Book Your Stay
              </Button>
            </div>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default StaticRooms;
