// <PERSON>ript to initialize admin accounts in Firebase
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { doc, setDoc, Timestamp } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { Admin } from '@/types/hotel';

// Admin accounts to create
const adminAccounts = [
  {
    email: '<EMAIL>',
    password: 'Admin@123456',
    name: 'Super Administrator',
    role: 'super_admin' as const,
    permissions: [
      'manage_users',
      'manage_admins',
      'manage_hotels',
      'manage_bookings',
      'view_analytics',
      'manage_settings',
      'manage_payments',
      'manage_reviews',
      'system_admin'
    ]
  },
  {
    email: '<EMAIL>',
    password: 'Manager@123456',
    name: 'Hotel Manager',
    role: 'admin' as const,
    permissions: [
      'manage_hotels',
      'manage_bookings',
      'view_analytics',
      'manage_reviews',
      'manage_rooms'
    ]
  },
  {
    email: '<EMAIL>',
    password: 'Staff@123456',
    name: 'Hotel Staff',
    role: 'manager' as const,
    permissions: [
      'manage_bookings',
      'view_analytics',
      'manage_reviews'
    ]
  }
];

// Function to create admin account
export const createAdminAccount = async (adminData: typeof adminAccounts[0]) => {
  try {
    console.log(`Creating admin account for: ${adminData.email}`);
    
    // Create Firebase user
    const userCredential = await createUserWithEmailAndPassword(
      auth, 
      adminData.email, 
      adminData.password
    );
    const user = userCredential.user;

    // Update Firebase profile
    await updateProfile(user, {
      displayName: adminData.name
    });

    // Create admin document in Firestore
    const adminDoc: Admin = {
      id: user.uid,
      name: adminData.name,
      email: adminData.email,
      role: adminData.role,
      permissions: adminData.permissions,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLogin: new Date()
    };

    await setDoc(doc(db, 'admins', user.uid), adminDoc);
    
    console.log(`✅ Admin account created successfully: ${adminData.email}`);
    console.log(`   - Name: ${adminData.name}`);
    console.log(`   - Role: ${adminData.role}`);
    console.log(`   - UID: ${user.uid}`);
    
    return user.uid;
  } catch (error: any) {
    if (error.code === 'auth/email-already-in-use') {
      console.log(`⚠️  Admin account already exists: ${adminData.email}`);
    } else {
      console.error(`❌ Error creating admin account ${adminData.email}:`, error.message);
    }
    throw error;
  }
};

// Function to initialize all admin accounts
export const initializeAllAdmins = async () => {
  console.log('🚀 Initializing admin accounts...\n');
  
  const results = [];
  
  for (const adminData of adminAccounts) {
    try {
      const uid = await createAdminAccount(adminData);
      results.push({ email: adminData.email, uid, status: 'success' });
    } catch (error) {
      results.push({ email: adminData.email, uid: null, status: 'error', error });
    }
    console.log(''); // Add spacing between accounts
  }
  
  console.log('📊 Admin Initialization Summary:');
  console.log('================================');
  results.forEach(result => {
    const status = result.status === 'success' ? '✅' : '❌';
    console.log(`${status} ${result.email} - ${result.status.toUpperCase()}`);
    if (result.uid) {
      console.log(`   UID: ${result.uid}`);
    }
  });
  
  console.log('\n🔐 Admin Credentials:');
  console.log('=====================');
  adminAccounts.forEach(admin => {
    console.log(`📧 ${admin.email}`);
    console.log(`🔑 ${admin.password}`);
    console.log(`👤 ${admin.name} (${admin.role})`);
    console.log('---');
  });
  
  return results;
};

// Function to reset admin password
export const resetAdminPassword = async (email: string, newPassword: string) => {
  try {
    // Note: This would require Firebase Admin SDK for production
    // For development, you can manually reset through Firebase Console
    console.log(`To reset password for ${email}:`);
    console.log('1. Go to Firebase Console > Authentication > Users');
    console.log(`2. Find user with email: ${email}`);
    console.log('3. Click "Reset Password" or "Edit User"');
    console.log(`4. Set new password: ${newPassword}`);
  } catch (error) {
    console.error('Error resetting admin password:', error);
    throw error;
  }
};

// Export admin credentials for reference
export const ADMIN_CREDENTIALS = {
  SUPER_ADMIN: {
    email: '<EMAIL>',
    password: 'Admin@123456',
    role: 'super_admin'
  },
  HOTEL_MANAGER: {
    email: '<EMAIL>',
    password: 'Manager@123456',
    role: 'admin'
  },
  STAFF: {
    email: '<EMAIL>',
    password: 'Staff@123456',
    role: 'manager'
  }
};

// Usage instructions
export const USAGE_INSTRUCTIONS = `
🚀 HOW TO INITIALIZE ADMIN ACCOUNTS:

1. Open browser console on your app
2. Run: initializeAllAdmins()
3. Check console for results

📧 ADMIN LOGIN CREDENTIALS:

Super Admin:
- Email: <EMAIL>
- Password: Admin@123456

Hotel Manager:
- Email: <EMAIL>  
- Password: Manager@123456

Staff:
- Email: <EMAIL>
- Password: Staff@123456

🔐 SECURITY NOTES:
- Change passwords after first login
- Use strong passwords in production
- Enable 2FA for admin accounts
- Regularly audit admin permissions
`;

console.log(USAGE_INSTRUCTIONS);
