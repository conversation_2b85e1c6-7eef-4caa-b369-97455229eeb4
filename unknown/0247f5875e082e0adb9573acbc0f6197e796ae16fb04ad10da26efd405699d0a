// Authentication service for Firebase Auth
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  updateProfile,
  sendPasswordResetEmail
} from 'firebase/auth';
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  Timestamp
} from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { Admin, User } from '@/types/hotel';

// Collections
const ADMINS_COLLECTION = 'admins';
const USERS_COLLECTION = 'users';

// Admin Authentication Functions
export const signInAdmin = async (email: string, password: string): Promise<{ user: FirebaseUser; admin: Admin }> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Check if user is an admin
    const adminDoc = await getDoc(doc(db, ADMINS_COLLECTION, user.uid));
    
    if (!adminDoc.exists()) {
      await signOut(auth);
      throw new Error('Access denied. Admin privileges required.');
    }
    
    const adminData = adminDoc.data() as Admin;
    
    if (!adminData.isActive) {
      await signOut(auth);
      throw new Error('Admin account is deactivated.');
    }
    
    // Update last login
    await updateDoc(doc(db, ADMINS_COLLECTION, user.uid), {
      lastLogin: Timestamp.now()
    });
    
    return { user, admin: { id: adminDoc.id, ...adminData } };
  } catch (error) {
    console.error('Error signing in admin:', error);
    throw error;
  }
};

export const createAdmin = async (
  email: string,
  password: string,
  adminData: Omit<Admin, 'id' | 'createdAt' | 'lastLogin'>
): Promise<string> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Update display name
    await updateProfile(user, {
      displayName: adminData.name
    });
    
    // Create admin document
    await setDoc(doc(db, ADMINS_COLLECTION, user.uid), {
      ...adminData,
      email,
      createdAt: Timestamp.now()
    });
    
    return user.uid;
  } catch (error) {
    console.error('Error creating admin:', error);
    throw error;
  }
};

export const getAdmin = async (adminId: string): Promise<Admin | null> => {
  try {
    const docRef = doc(db, ADMINS_COLLECTION, adminId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Admin;
    }
    return null;
  } catch (error) {
    console.error('Error getting admin:', error);
    throw error;
  }
};

export const updateAdmin = async (adminId: string, updates: Partial<Admin>): Promise<void> => {
  try {
    const docRef = doc(db, ADMINS_COLLECTION, adminId);
    await updateDoc(docRef, updates);
  } catch (error) {
    console.error('Error updating admin:', error);
    throw error;
  }
};

// User Authentication Functions
export const signInUser = async (email: string, password: string): Promise<void> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Check if user document exists
    const userDoc = await getDoc(doc(db, USERS_COLLECTION, user.uid));

    if (!userDoc.exists()) {
      await signOut(auth);
      throw new Error('User account not found. Please contact support.');
    }

    const userData = userDoc.data() as User;

    if (!userData.isActive) {
      await signOut(auth);
      throw new Error('User account is deactivated. Please contact support.');
    }

    // Update last login
    await updateDoc(doc(db, USERS_COLLECTION, user.uid), {
      lastLogin: new Date(),
      updatedAt: new Date()
    });

    console.log('User signed in successfully:', user.uid);
  } catch (error) {
    console.error('Error signing in user:', error);
    throw error;
  }
};



export const getUser = async (userId: string): Promise<User | null> => {
  try {
    const docRef = doc(db, USERS_COLLECTION, userId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as User;
    }
    return null;
  } catch (error) {
    console.error('Error getting user:', error);
    throw error;
  }
};

export const updateUser = async (userId: string, updates: Partial<User>): Promise<void> => {
  try {
    const docRef = doc(db, USERS_COLLECTION, userId);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

export const signUpUser = async (userData: {
  email: string;
  password: string;
  name: string;
  phone: string;
  address?: string;
  city?: string;
}): Promise<void> => {
  try {
    // Create Firebase user
    const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);
    const user = userCredential.user;

    // Update Firebase profile
    await updateProfile(user, {
      displayName: userData.name
    });

    // Create user document in Firestore
    const userDoc: User = {
      id: user.uid,
      name: userData.name,
      email: userData.email,
      phone: userData.phone,
      address: userData.address || '',
      city: userData.city || '',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await setDoc(doc(db, USERS_COLLECTION, user.uid), userDoc);
    console.log('User created successfully:', user.uid);
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};



// General Authentication Functions
export const signOutUser = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw error;
  }
};

// Auth state observer
export const onAuthStateChange = (callback: (user: FirebaseUser | null) => void) => {
  return onAuthStateChanged(auth, callback);
};

// Check if current user is admin
export const isCurrentUserAdmin = async (): Promise<boolean> => {
  try {
    const user = auth.currentUser;
    if (!user) return false;
    
    const adminDoc = await getDoc(doc(db, ADMINS_COLLECTION, user.uid));
    return adminDoc.exists() && adminDoc.data()?.isActive === true;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};

// Get current user's admin data
export const getCurrentAdmin = async (): Promise<Admin | null> => {
  try {
    const user = auth.currentUser;
    if (!user) return null;
    
    return await getAdmin(user.uid);
  } catch (error) {
    console.error('Error getting current admin:', error);
    return null;
  }
};
