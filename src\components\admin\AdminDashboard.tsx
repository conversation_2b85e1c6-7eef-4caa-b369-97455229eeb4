// Admin dashboard main component
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getAllHotels, getHotelBookings } from '@/services/hotelService';
import { seedAllData, getSeedingStatus } from '@/services/seedService';
import RealTimeDashboard from '@/components/admin/RealTimeDashboard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Hotel,
  Bed,
  Calendar,
  Users,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  IndianRupee
} from 'lucide-react';
import { toast } from 'sonner';
import { Hotel as HotelType, Booking } from '@/types/hotel';

interface DashboardStats {
  totalHotels: number;
  totalRooms: number;
  totalBookings: number;
  pendingBookings: number;
  confirmedBookings: number;
  totalRevenue: number;
  occupancyRate: number;
}

const AdminDashboard: React.FC = () => {
  const { admin } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalHotels: 0,
    totalRooms: 0,
    totalBookings: 0,
    pendingBookings: 0,
    confirmedBookings: 0,
    totalRevenue: 0,
    occupancyRate: 0
  });
  const [recentBookings, setRecentBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [seedingStatus, setSeedingStatus] = useState({ isSeeded: false, hotelsCount: 0, roomsCount: 0 });
  const [seeding, setSeeding] = useState(false);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Check seeding status
      const seedStatus = await getSeedingStatus();
      setSeedingStatus(seedStatus);
      
      if (!seedStatus.isSeeded) {
        setLoading(false);
        return;
      }

      // Load hotels
      const hotels = await getAllHotels();
      
      // Load all bookings for statistics
      let allBookings: Booking[] = [];
      for (const hotel of hotels) {
        const hotelBookings = await getHotelBookings(hotel.id);
        allBookings = [...allBookings, ...hotelBookings];
      }

      // Calculate statistics
      const totalRooms = hotels.reduce((sum, hotel) => {
        return sum + 20; // Placeholder - would calculate from actual rooms
      }, 0);

      const pendingBookings = allBookings.filter(b => b.status === 'pending').length;
      const confirmedBookings = allBookings.filter(b => b.status === 'confirmed').length;
      const totalRevenue = allBookings
        .filter(b => b.status === 'confirmed' || b.status === 'completed')
        .reduce((sum, booking) => sum + booking.totalAmount, 0);

      setStats({
        totalHotels: hotels.length,
        totalRooms,
        totalBookings: allBookings.length,
        pendingBookings,
        confirmedBookings,
        totalRevenue,
        occupancyRate: totalRooms > 0 ? (confirmedBookings / totalRooms) * 100 : 0
      });

      // Set recent bookings (last 5)
      const sortedBookings = allBookings
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5);
      setRecentBookings(sortedBookings);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleSeedData = async () => {
    try {
      setSeeding(true);
      toast.info('Seeding data... This may take a moment.');
      
      await seedAllData();
      
      toast.success('Sample data seeded successfully!');
      await loadDashboardData();
    } catch (error) {
      console.error('Error seeding data:', error);
      toast.error('Failed to seed data. Please try again.');
    } finally {
      setSeeding(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      case 'completed':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!seedingStatus.isSeeded) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Welcome to Admin Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Welcome, {admin?.name}! Let's get started by setting up your hotel data.
          </p>
        </div>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No hotel data found. Click the button below to seed the database with sample Tiruvannamalai hotels.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle>Setup Sample Data</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              This will create sample hotels, rooms, and admin accounts for Tiruvannamalai. 
              Perfect for getting started quickly!
            </p>
            <Button 
              onClick={handleSeedData} 
              disabled={seeding}
              className="w-full sm:w-auto"
            >
              {seeding ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Setting up data...
                </div>
              ) : (
                'Setup Sample Data'
              )}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Real-time Dashboard */}
      <RealTimeDashboard />

      {/* Legacy Dashboard Content - keeping for reference */}
      <div className="border-t pt-6">
        <h2 className="text-xl font-semibold mb-4">Additional Statistics</h2>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Hotels</CardTitle>
            <Hotel className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalHotels}</div>
            <p className="text-xs text-muted-foreground">
              Active properties in Tiruvannamalai
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalBookings}</div>
            <p className="text-xs text-muted-foreground">
              {stats.pendingBookings} pending, {stats.confirmedBookings} confirmed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Total confirmed bookings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.occupancyRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Current occupancy across all hotels
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Bookings */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Bookings</CardTitle>
        </CardHeader>
        <CardContent>
          {recentBookings.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No bookings yet</p>
              <p className="text-sm">Bookings will appear here once customers start making reservations.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-3">
                      <div>
                        <p className="font-medium">{booking.guestInfo.name}</p>
                        <p className="text-sm text-muted-foreground">{booking.guestInfo.email}</p>
                      </div>
                    </div>
                    <div className="mt-2 text-sm text-muted-foreground">
                      {new Date(booking.checkInDate).toLocaleDateString()} - {new Date(booking.checkOutDate).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={getStatusBadgeVariant(booking.status)}>
                      {booking.status}
                    </Badge>
                    <p className="text-sm font-medium mt-1">₹{booking.totalAmount.toLocaleString()}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Hotel className="h-6 w-6" />
              <span>Add New Hotel</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Bed className="h-6 w-6" />
              <span>Manage Rooms</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Users className="h-6 w-6" />
              <span>View All Bookings</span>
            </Button>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
