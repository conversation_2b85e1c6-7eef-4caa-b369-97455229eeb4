// Service for seeding initial data to Firebase
import { collection, doc, writeBatch, getDocs, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { sampleTiruvannamalaiHotels, sampleRooms } from '@/data/sampleHotels';
import { createAdmin } from '@/services/authService';
import { Admin } from '@/types/hotel';

// Collections
const HOTELS_COLLECTION = 'hotels';
const ROOMS_COLLECTION = 'rooms';

// Seed hotels and rooms
export const seedHotelsAndRooms = async (): Promise<void> => {
  try {
    console.log('Starting to seed hotels and rooms...');
    
    // Check if hotels already exist
    const hotelsQuery = query(
      collection(db, HOTELS_COLLECTION),
      where('city', '==', 'Tiruvannamalai')
    );
    const existingHotels = await getDocs(hotelsQuery);
    
    if (!existingHotels.empty) {
      console.log('Hotels already exist. Skipping seeding.');
      return;
    }
    
    const batch = writeBatch(db);
    const hotelIds: string[] = [];
    
    // Add hotels
    sampleTiruvannamalaiHotels.forEach((hotel, index) => {
      const hotelRef = doc(collection(db, HOTELS_COLLECTION));
      hotelIds.push(hotelRef.id);
      
      batch.set(hotelRef, {
        ...hotel,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log(`Added hotel: ${hotel.name} with ID: ${hotelRef.id}`);
    });
    
    // Add rooms with correct hotel IDs
    sampleRooms.forEach((room, index) => {
      const roomRef = doc(collection(db, ROOMS_COLLECTION));
      const hotelIndex = index < 2 ? 0 : index < 4 ? 1 : 2; // Distribute rooms among hotels
      
      batch.set(roomRef, {
        ...room,
        hotelId: hotelIds[hotelIndex],
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log(`Added room: ${room.name} for hotel ID: ${hotelIds[hotelIndex]}`);
    });
    
    await batch.commit();
    console.log('Successfully seeded hotels and rooms!');
    
  } catch (error) {
    console.error('Error seeding hotels and rooms:', error);
    throw error;
  }
};

// Seed admin users
export const seedAdminUsers = async (): Promise<void> => {
  try {
    console.log('Starting to seed admin users...');
    
    const adminUsers = [
      {
        email: '<EMAIL>',
        password: 'Admin@123',
        name: 'Super Admin',
        role: 'super_admin' as const,
        permissions: [
          'manage_hotels',
          'manage_rooms',
          'manage_bookings',
          'manage_users',
          'manage_admins',
          'view_analytics',
          'manage_reviews'
        ],
        isActive: true
      },
      {
        email: '<EMAIL>',
        password: 'HotelAdmin@123',
        name: 'Hotel Manager',
        role: 'hotel_admin' as const,
        permissions: [
          'manage_hotels',
          'manage_rooms',
          'manage_bookings',
          'view_analytics'
        ],
        isActive: true
      }
    ];
    
    for (const adminData of adminUsers) {
      try {
        const { email, password, ...adminInfo } = adminData;
        await createAdmin(email, password, adminInfo);
        console.log(`Created admin: ${adminData.name} (${email})`);
      } catch (error: any) {
        if (error.code === 'auth/email-already-in-use') {
          console.log(`Admin ${adminData.email} already exists. Skipping.`);
        } else {
          console.error(`Error creating admin ${adminData.email}:`, error);
        }
      }
    }
    
    console.log('Successfully seeded admin users!');
    
  } catch (error) {
    console.error('Error seeding admin users:', error);
    throw error;
  }
};

// Seed all data
export const seedAllData = async (): Promise<void> => {
  try {
    console.log('Starting complete data seeding...');
    
    await seedHotelsAndRooms();
    await seedAdminUsers();
    
    console.log('All data seeded successfully!');
    
  } catch (error) {
    console.error('Error in complete data seeding:', error);
    throw error;
  }
};

// Clear all data (for development/testing)
export const clearAllData = async (): Promise<void> => {
  try {
    console.log('Starting to clear all data...');
    
    // Clear hotels
    const hotelsSnapshot = await getDocs(collection(db, HOTELS_COLLECTION));
    const batch1 = writeBatch(db);
    
    hotelsSnapshot.docs.forEach(doc => {
      batch1.delete(doc.ref);
    });
    
    await batch1.commit();
    console.log('Cleared hotels');
    
    // Clear rooms
    const roomsSnapshot = await getDocs(collection(db, ROOMS_COLLECTION));
    const batch2 = writeBatch(db);
    
    roomsSnapshot.docs.forEach(doc => {
      batch2.delete(doc.ref);
    });
    
    await batch2.commit();
    console.log('Cleared rooms');
    
    console.log('All data cleared successfully!');
    
  } catch (error) {
    console.error('Error clearing data:', error);
    throw error;
  }
};

// Get seeding status
export const getSeedingStatus = async (): Promise<{
  hotelsCount: number;
  roomsCount: number;
  isSeeded: boolean;
}> => {
  try {
    const hotelsSnapshot = await getDocs(
      query(collection(db, HOTELS_COLLECTION), where('city', '==', 'Tiruvannamalai'))
    );
    const roomsSnapshot = await getDocs(collection(db, ROOMS_COLLECTION));
    
    const hotelsCount = hotelsSnapshot.size;
    const roomsCount = roomsSnapshot.size;
    const isSeeded = hotelsCount > 0 && roomsCount > 0;
    
    return {
      hotelsCount,
      roomsCount,
      isSeeded
    };
  } catch (error) {
    console.error('Error getting seeding status:', error);
    throw error;
  }
};
