// Enhanced Sign Up Page with Scroll Options and Full Responsiveness
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { signUpUser } from '@/services/authService';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  User,
  Mail,
  Phone,
  Lock,
  MapPin,
  Eye,
  EyeOff,
  ArrowRight,
  Star,
  Shield,
  Heart,
  Sparkles,
  ArrowLeft,
  Loader2,
  Building,
  Users,
  Award,
  Clock,
  X
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const SignUpPage: React.FC = () => {
  const navigate = useNavigate();
  const { refreshUserData, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const formRef = useRef<HTMLDivElement>(null);
  const benefitsRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    address: '',
    city: '',
    agreeToTerms: false
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/profile');
    }
  }, [isAuthenticated, navigate]);

  // Scroll tracking and animations
  useEffect(() => {
    // Check if mobile device
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      setScrollProgress(scrollPercent);

      // Trigger visibility animations
      if (scrollTop > (isMobile ? 50 : 100)) {
        setIsVisible(true);
      }
    };

    const handleScrollToSection = () => {
      // Intersection Observer for smooth animations
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.classList.add('animate-in', 'slide-in-from-bottom', 'duration-700');
            }
          });
        },
        { threshold: 0.1 }
      );

      // Observe benefit cards
      if (benefitsRef.current) {
        const benefitCards = benefitsRef.current.querySelectorAll('.benefit-card');
        benefitCards.forEach((card) => observer.observe(card));
      }

      return () => observer.disconnect();
    };

    window.addEventListener('scroll', handleScroll);
    const cleanup = handleScrollToSection();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      cleanup();
    };
  }, []);

  // Smooth scroll to form section
  const scrollToForm = () => {
    formRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };

  // Smooth scroll to benefits
  const scrollToBenefits = () => {
    benefitsRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };

  // Check if form has data
  const hasFormData = () => {
    return formData.name || formData.email || formData.phone || formData.password || formData.address || formData.city;
  };

  // Handle close/back navigation
  const handleClose = () => {
    // If form has data, show confirmation
    if (hasFormData()) {
      const confirmed = window.confirm(
        'Are you sure you want to leave? Your progress will be lost.'
      );
      if (!confirmed) return;
    }

    // Check if user came from another page in the app
    if (window.history.length > 1) {
      navigate(-1); // Go back to previous page
    } else {
      navigate('/'); // Go to home page
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => document.removeEventListener('keydown', handleEscapeKey);
  }, []);

  // Form validation
  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!formData.name.trim()) newErrors.name = 'Full name is required';
      if (!formData.email.trim()) newErrors.email = 'Email is required';
      else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';
      if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
      else if (!/^\+?[\d\s-()]{10,}$/.test(formData.phone)) newErrors.phone = 'Phone number is invalid';
    }

    if (step === 2) {
      if (!formData.password) newErrors.password = 'Password is required';
      else if (formData.password.length < 6) newErrors.password = 'Password must be at least 6 characters';
      if (!formData.confirmPassword) newErrors.confirmPassword = 'Please confirm your password';
      else if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = 'Passwords do not match';
      if (!formData.address.trim()) newErrors.address = 'Address is required';
      if (!formData.city.trim()) newErrors.city = 'City is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(2);
      // Smooth scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handlePrevStep = () => {
    setCurrentStep(1);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep(2)) return;
    if (!formData.agreeToTerms) {
      toast.error('Please agree to the terms and conditions');
      return;
    }

    setLoading(true);

    try {
      await signUpUser({
        email: formData.email,
        password: formData.password,
        name: formData.name,
        phone: formData.phone,
        address: formData.address,
        city: formData.city
      });

      await refreshUserData();
      toast.success(`Welcome to One Touch Hotels, ${formData.name}!`);
      navigate('/profile');
    } catch (error: any) {
      toast.error(error.message || 'Failed to create account');
    } finally {
      setLoading(false);
    }
  };

  // Benefits data
  const benefits = [
    {
      icon: Star,
      title: 'Exclusive Offers',
      description: 'Get access to member-only deals and discounts'
    },
    {
      icon: Shield,
      title: 'Secure Booking',
      description: 'Your data is protected with enterprise-grade security'
    },
    {
      icon: Heart,
      title: 'Personalized Experience',
      description: 'Tailored recommendations based on your preferences'
    },
    {
      icon: Clock,
      title: '24/7 Support',
      description: 'Round-the-clock customer service for your convenience'
    }
  ];

  const stats = [
    { icon: Building, value: '50+', label: 'Premium Hotels' },
    { icon: Users, value: '10K+', label: 'Happy Guests' },
    { icon: Award, value: '4.9', label: 'Average Rating' },
    { icon: Star, value: '99%', label: 'Satisfaction Rate' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative pt-20">
      {/* Scroll Progress Bar */}
      <div className="fixed top-16 left-0 w-full h-1 bg-gray-200 z-50">
        <div
          className="h-full bg-gradient-to-r from-primary to-purple-600 transition-all duration-300 ease-out"
          style={{ width: `${scrollProgress}%` }}
        />
      </div>

      {/* Floating Action Buttons */}
      {isVisible && (
        <div className={cn(
          "fixed z-40 flex gap-2",
          isMobile
            ? "bottom-4 left-1/2 transform -translate-x-1/2 flex-row"
            : "right-4 bottom-20 flex-col"
        )}>
          <Button
            onClick={scrollToForm}
            size={isMobile ? "default" : "sm"}
            className={cn(
              "shadow-lg hover:shadow-xl transition-all duration-300",
              isMobile
                ? "rounded-full px-4 h-12"
                : "rounded-full w-12 h-12"
            )}
            title="Go to Form"
          >
            <ArrowRight className="w-4 h-4" />
            {isMobile && <span className="ml-2 text-sm">Sign Up</span>}
          </Button>

          {!isMobile && (
            <>
              <Button
                onClick={scrollToBenefits}
                variant="outline"
                size="sm"
                className="rounded-full w-12 h-12 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 backdrop-blur-sm"
                title="View Benefits"
              >
                <Star className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                variant="outline"
                size="sm"
                className="rounded-full w-12 h-12 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 backdrop-blur-sm"
                title="Back to Top"
              >
                ↑
              </Button>
            </>
          )}

          {isMobile && (
            <Button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              variant="outline"
              size="default"
              className="rounded-full px-4 h-12 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 backdrop-blur-sm"
              title="Back to Top"
            >
              ↑ <span className="ml-2 text-sm">Top</span>
            </Button>
          )}
        </div>
      )}

      {/* Close Button - Fixed Position */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClose}
        className="fixed top-20 right-4 z-50 rounded-full w-10 h-10 p-0 hover:bg-red-50 hover:text-red-600 transition-all duration-200 group bg-white/90 backdrop-blur-sm shadow-lg"
        title="Close Sign Up (ESC)"
      >
        <X className="w-5 h-5 text-gray-500 group-hover:text-red-600 transition-colors" />
      </Button>

      <div className="container mx-auto px-4 py-8 lg:py-12">
        {/* Mobile Welcome Banner */}
        <div className="lg:hidden mb-8 text-center pt-4">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            Join One Touch Hotels
          </h1>
          <p className="text-gray-600 text-sm">
            Create your account for exclusive benefits
          </p>
          <Button
            onClick={scrollToForm}
            className="mt-4 w-full sm:w-auto"
            size="lg"
          >
            Get Started
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 max-w-7xl mx-auto">
          {/* Left Side - Benefits & Info */}
          <div className="space-y-8 lg:sticky lg:top-24 lg:h-fit">
            {/* Hero Section */}
            <div className="text-center lg:text-left">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Join Our
                <span className="text-primary block sm:inline sm:ml-2">
                  Hotel Community
                </span>
              </h1>
              <p className="text-lg text-gray-600 mb-6">
                Create your account and unlock exclusive benefits, personalized experiences, 
                and seamless booking across our premium hotel network.
              </p>
              
              {/* Stats */}
              <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-2 xl:grid-cols-4 gap-4 mb-8">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center p-4 bg-white/60 rounded-xl border border-gray-200">
                    <stat.icon className="w-6 h-6 text-primary mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                    <div className="text-xs text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Benefits */}
            <div ref={benefitsRef} className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-900 text-center lg:text-left">
                Why Join Us?
              </h2>
              <div className="grid sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4">
                {benefits.map((benefit, index) => (
                  <div
                    key={index}
                    className="benefit-card flex items-start gap-3 p-4 bg-white/60 rounded-xl border border-gray-200 hover:bg-white/80 hover:scale-105 transition-all duration-300 hover:shadow-lg"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="p-2 bg-primary/10 rounded-lg flex-shrink-0 group-hover:bg-primary/20 transition-colors">
                      <benefit.icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">{benefit.title}</h3>
                      <p className="text-sm text-gray-600">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="bg-gradient-to-r from-primary/5 to-purple-100/50 rounded-2xl p-6 text-center lg:text-left">
              <div className="flex items-center justify-center lg:justify-start gap-2 mb-3">
                <Shield className="w-5 h-5 text-primary" />
                <span className="font-semibold text-gray-900">Trusted & Secure</span>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Your personal information is protected with bank-level encryption and security measures.
              </p>
              <div className="flex items-center justify-center lg:justify-start gap-4">
                <Badge variant="secondary" className="text-xs">SSL Encrypted</Badge>
                <Badge variant="secondary" className="text-xs">GDPR Compliant</Badge>
                <Badge variant="secondary" className="text-xs">ISO 27001</Badge>
              </div>
            </div>
          </div>

          {/* Right Side - Sign Up Form */}
          <div ref={formRef} className="w-full">
            <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 relative">
              {/* Card Close Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="absolute top-4 right-4 rounded-full w-8 h-8 p-0 hover:bg-red-50 transition-all duration-200 z-10 group"
                title="Close Sign Up (ESC)"
              >
                <X className="w-4 h-4 text-gray-400 group-hover:text-red-500 transition-colors" />
              </Button>

              <CardHeader className="text-center pb-6 pr-12">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  Create Your Account
                </CardTitle>
                <p className="text-gray-600 mt-2">
                  Step {currentStep} of 2 - {currentStep === 1 ? 'Personal Information' : 'Account Security'}
                </p>

                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 mt-4">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${(currentStep / 2) * 100}%` }}
                  />
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Step 1: Personal Information */}
                  {currentStep === 1 && (
                    <div className="space-y-4 animate-in slide-in-from-right duration-300">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name *</Label>
                        <div className="relative">
                          <Input
                            id="name"
                            type="text"
                            value={formData.name}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                            placeholder="Enter your full name"
                            className={cn("pl-10", errors.name && "border-red-500")}
                          />
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        </div>
                        {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address *</Label>
                        <div className="relative">
                          <Input
                            id="email"
                            type="email"
                            value={formData.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            placeholder="Enter your email address"
                            className={cn("pl-10", errors.email && "border-red-500")}
                          />
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        </div>
                        {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number *</Label>
                        <div className="relative">
                          <Input
                            id="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            placeholder="+91 9876543210"
                            className={cn("pl-10", errors.phone && "border-red-500")}
                          />
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        </div>
                        {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
                      </div>

                      <Button 
                        type="button" 
                        onClick={handleNextStep}
                        className="w-full h-12 text-base font-semibold"
                        size="lg"
                      >
                        Continue
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  )}

                  {/* Step 2: Account Security & Address */}
                  {currentStep === 2 && (
                    <div className="space-y-4 animate-in slide-in-from-right duration-300">
                      <div className="grid sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="password">Password *</Label>
                          <div className="relative">
                            <Input
                              id="password"
                              type={showPassword ? "text" : "password"}
                              value={formData.password}
                              onChange={(e) => handleInputChange('password', e.target.value)}
                              placeholder="Create a strong password"
                              className={cn("pl-10 pr-10", errors.password && "border-red-500")}
                            />
                            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                            </Button>
                          </div>
                          {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="confirmPassword">Confirm Password *</Label>
                          <div className="relative">
                            <Input
                              id="confirmPassword"
                              type={showConfirmPassword ? "text" : "password"}
                              value={formData.confirmPassword}
                              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                              placeholder="Confirm your password"
                              className={cn("pl-10 pr-10", errors.confirmPassword && "border-red-500")}
                            />
                            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            >
                              {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                            </Button>
                          </div>
                          {errors.confirmPassword && <p className="text-sm text-red-500">{errors.confirmPassword}</p>}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="address">Address *</Label>
                        <div className="relative">
                          <Input
                            id="address"
                            type="text"
                            value={formData.address}
                            onChange={(e) => handleInputChange('address', e.target.value)}
                            placeholder="Enter your full address"
                            className={cn("pl-10", errors.address && "border-red-500")}
                          />
                          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        </div>
                        {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="city">City *</Label>
                        <div className="relative">
                          <Input
                            id="city"
                            type="text"
                            value={formData.city}
                            onChange={(e) => handleInputChange('city', e.target.value)}
                            placeholder="Enter your city"
                            className={cn("pl-10", errors.city && "border-red-500")}
                          />
                          <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        </div>
                        {errors.city && <p className="text-sm text-red-500">{errors.city}</p>}
                      </div>

                      {/* Terms and Conditions */}
                      <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                        <input
                          type="checkbox"
                          id="agreeToTerms"
                          checked={formData.agreeToTerms}
                          onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                          className="mt-1 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label htmlFor="agreeToTerms" className="text-sm text-gray-600 leading-relaxed">
                          I agree to the{' '}
                          <Link to="/terms" className="text-primary hover:underline">
                            Terms of Service
                          </Link>{' '}
                          and{' '}
                          <Link to="/privacy" className="text-primary hover:underline">
                            Privacy Policy
                          </Link>
                        </label>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3">
                        <Button 
                          type="button" 
                          variant="outline"
                          onClick={handlePrevStep}
                          className="flex-1 h-12"
                        >
                          <ArrowLeft className="w-4 h-4 mr-2" />
                          Back
                        </Button>
                        <Button 
                          type="submit" 
                          disabled={loading || !formData.agreeToTerms}
                          className="flex-1 h-12 text-base font-semibold"
                        >
                          {loading ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Creating Account...
                            </>
                          ) : (
                            <>
                              Create Account
                              <Sparkles className="w-4 h-4 ml-2" />
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  )}
                </form>

                {/* Sign In Link */}
                <Separator />
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Already have an account?{' '}
                    <Link to="/signin" className="text-primary hover:underline font-medium">
                      Sign in here
                    </Link>
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-200 py-8 mt-12">
        <div className="container mx-auto px-4 text-center">
          <p className="text-sm text-gray-600">
            © 2024 One Touch Hotels. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SignUpPage;
