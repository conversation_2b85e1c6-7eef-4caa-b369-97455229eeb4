
import React from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';

interface AboutProps {
  className?: string;
}

const About: React.FC<AboutProps> = ({ className }) => {
  return (
    <section id="about" className={cn('py-20 md:py-32 bg-gray-50', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid md:grid-cols-12 gap-12 md:gap-20 items-start">
          <FadeIn className="md:col-span-5">
            <div className="flex flex-col space-y-6">
              <div>
                <span className="text-sm md:text-base font-medium text-primary mb-2 inline-block">About us</span>
                <h2 className="text-3xl md:text-5xl font-serif font-medium tracking-tight mb-6">Your home away from home since 2020</h2>
              </div>
              
              <p className="text-lg text-muted-foreground">
                One Touch Park Hotel has been providing exceptional hospitality in the heart of the city for over four years.
              </p>
              <p className="text-lg text-muted-foreground">
                We combine modern luxury with personalized service, creating unforgettable experiences for business travelers, families, and tourists alike. Our commitment to excellence has made us a preferred choice for discerning guests.
              </p>
            </div>
          </FadeIn>
          
          <FadeIn delay={150} className="md:col-span-7">
            <div className="relative h-[500px] lg:h-[600px] w-full rounded-lg overflow-hidden">
              <img 
                src="/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png"
                alt="Elegant hotel lobby with modern design"
                className="w-full h-full object-cover"
              />
            </div>
          </FadeIn>
        </div>
      </div>
    </section>
  );
};

export default About;
