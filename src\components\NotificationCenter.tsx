// Enhanced Real-time Notification Center with Advanced Features
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { RealtimeService, Notification, markNotificationAsRead } from '@/services/realtimeService';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Bell,
  Check,
  Calendar,
  AlertCircle,
  MessageSquare,
  Hotel,
  X,
  Search,
  Filter,
  Settings,
  Trash2,
  Star,
  Clock,
  Eye,
  EyeOff,
  Volume2,
  VolumeX,
  Maximize2,
  RefreshCw,
  Archive,
  Tag,
  Users,
  Gift,
  CreditCard,
  Wifi,
  Car,
  Utensils,
  Coffee,
  Dumbbell,
  Waves,
  Shield,
  Briefcase,
  MapPin
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface NotificationCenterProps {
  className?: string;
}

// Enhanced notification types
type NotificationCategory = 'all' | 'bookings' | 'amenities' | 'offers' | 'alerts' | 'system';
type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';
type ViewMode = 'dropdown' | 'modal' | 'sidebar';

interface EnhancedNotification extends Notification {
  category?: NotificationCategory;
  priority?: NotificationPriority;
  actionUrl?: string;
  imageUrl?: string;
  tags?: string[];
  isStarred?: boolean;
  isArchived?: boolean;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ className }) => {
  const { firebaseUser, isAdmin, admin } = useAuth();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<EnhancedNotification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('dropdown');
  const [selectedCategory, setSelectedCategory] = useState<NotificationCategory>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showOnlyUnread, setShowOnlyUnread] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState<Set<string>>(new Set());
  const [sortBy, setSortBy] = useState<'date' | 'priority' | 'category'>('date');
  const [isViewAllOpen, setIsViewAllOpen] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  const notificationRef = useRef<HTMLDivElement>(null);

  // Enhanced notification categories
  const categories = [
    { id: 'all' as const, label: 'All', icon: Bell, count: notifications.length },
    { id: 'bookings' as const, label: 'Bookings', icon: Calendar, count: notifications.filter(n => n.category === 'bookings').length },
    { id: 'amenities' as const, label: 'Amenities', icon: Hotel, count: notifications.filter(n => n.category === 'amenities').length },
    { id: 'offers' as const, label: 'Offers', icon: Gift, count: notifications.filter(n => n.category === 'offers').length },
    { id: 'alerts' as const, label: 'Alerts', icon: AlertCircle, count: notifications.filter(n => n.category === 'alerts').length },
    { id: 'system' as const, label: 'System', icon: Settings, count: notifications.filter(n => n.category === 'system').length }
  ];

  // Filter and sort notifications
  const filteredNotifications = useCallback(() => {
    let filtered = notifications;

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(n => n.category === selectedCategory);
    }

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(n =>
        n.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        n.message.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Unread filter
    if (showOnlyUnread) {
      filtered = filtered.filter(n => !n.isRead);
    }

    // Sort notifications
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          return (priorityOrder[b.priority || 'low'] || 1) - (priorityOrder[a.priority || 'low'] || 1);
        case 'category':
          return (a.category || '').localeCompare(b.category || '');
        case 'date':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

    return filtered;
  }, [notifications, selectedCategory, searchQuery, showOnlyUnread, sortBy]);

  // Play notification sound
  const playNotificationSound = useCallback(() => {
    if (soundEnabled && audioRef.current) {
      audioRef.current.play().catch(console.error);
    }
  }, [soundEnabled]);

  // Generate mock notifications for demo
  const generateMockNotifications = useCallback((): EnhancedNotification[] => {
    const mockNotifications: EnhancedNotification[] = [
      {
        id: '1',
        title: 'Booking Confirmed',
        message: 'Your reservation for Deluxe Suite has been confirmed for Dec 25-27, 2024.',
        type: 'booking_confirmed',
        category: 'bookings',
        priority: 'high',
        isRead: false,
        createdAt: new Date(Date.now() - 5 * 60 * 1000),
        actionUrl: '/bookings/1',
        imageUrl: '/images/suite.jpg',
        tags: ['booking', 'confirmed', 'deluxe']
      },
      {
        id: '2',
        title: 'Special Offer: 20% Off Spa Services',
        message: 'Enjoy 20% off all spa treatments this weekend. Book now!',
        type: 'admin_message',
        category: 'offers',
        priority: 'medium',
        isRead: false,
        createdAt: new Date(Date.now() - 30 * 60 * 1000),
        actionUrl: '/spa',
        imageUrl: '/images/spa.jpg',
        tags: ['offer', 'spa', 'discount']
      },
      {
        id: '3',
        title: 'Pool Maintenance Complete',
        message: 'The rooftop pool is now open and ready for your enjoyment.',
        type: 'room_availability',
        category: 'amenities',
        priority: 'low',
        isRead: true,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        actionUrl: '/amenities',
        imageUrl: '/images/pool.jpg',
        tags: ['pool', 'maintenance', 'open']
      },
      {
        id: '4',
        title: 'WiFi Upgrade Complete',
        message: 'High-speed WiFi has been upgraded throughout the hotel.',
        type: 'admin_message',
        category: 'system',
        priority: 'low',
        isRead: false,
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        tags: ['wifi', 'upgrade', 'system']
      },
      {
        id: '5',
        title: 'Room Service Available',
        message: '24/7 room service is now available. Order from our premium menu.',
        type: 'admin_message',
        category: 'amenities',
        priority: 'medium',
        isRead: false,
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        actionUrl: '/room-service',
        tags: ['room-service', 'available', '24/7']
      }
    ];
    return mockNotifications;
  }, []);

  // Initialize with mock notifications for demo
  useEffect(() => {
    if (!firebaseUser) return;

    // Load mock notifications for demo
    const mockNotifications = generateMockNotifications();
    setNotifications(mockNotifications);

    // Try to connect to real-time service if available
    try {
      const realtimeService = RealtimeService.getInstance();
      let listenerId: string;

      if (isAdmin && admin) {
        listenerId = realtimeService.subscribeToAdminNotifications(
          admin.id,
          (newNotifications) => {
            // Enhance notifications with additional properties
            const enhancedNotifications = newNotifications.map(n => ({
              ...n,
              category: getCategoryFromType(n.type),
              priority: getPriorityFromType(n.type),
              tags: generateTagsFromNotification(n)
            }));

            setNotifications(enhancedNotifications);

            // Play sound for new notifications
            const unreadCount = enhancedNotifications.filter(n => !n.isRead).length;
            if (unreadCount > 0) {
              playNotificationSound();
              toast.info(`You have ${unreadCount} new notification${unreadCount > 1 ? 's' : ''}`);
            }
          }
        );
      } else {
        listenerId = realtimeService.subscribeToUserNotifications(
          firebaseUser.uid,
          (newNotifications) => {
            // Enhance notifications with additional properties
            const enhancedNotifications = newNotifications.map(n => ({
              ...n,
              category: getCategoryFromType(n.type),
              priority: getPriorityFromType(n.type),
              tags: generateTagsFromNotification(n)
            }));

            setNotifications(enhancedNotifications);

            // Play sound for new notifications
            const unreadCount = enhancedNotifications.filter(n => !n.isRead).length;
            if (unreadCount > 0) {
              playNotificationSound();
              toast.info(`You have ${unreadCount} new notification${unreadCount > 1 ? 's' : ''}`);
            }
          }
        );
      }

      return () => {
        realtimeService.unsubscribe(listenerId);
      };
    } catch (error) {
      console.log('Real-time service not available, using mock data');
    }
  }, [firebaseUser, isAdmin, admin, generateMockNotifications, playNotificationSound]);

  // Helper functions
  const getCategoryFromType = (type: string): NotificationCategory => {
    switch (type) {
      case 'booking_confirmed':
      case 'booking_cancelled':
        return 'bookings';
      case 'room_availability':
        return 'amenities';
      case 'admin_message':
        return 'alerts';
      default:
        return 'system';
    }
  };

  const getPriorityFromType = (type: string): NotificationPriority => {
    switch (type) {
      case 'booking_confirmed':
      case 'booking_cancelled':
        return 'high';
      case 'room_availability':
        return 'medium';
      default:
        return 'low';
    }
  };

  const generateTagsFromNotification = (notification: Notification): string[] => {
    const tags: string[] = [notification.type];
    const title = notification.title.toLowerCase();
    const message = notification.message.toLowerCase();

    if (title.includes('booking') || message.includes('booking')) tags.push('booking');
    if (title.includes('room') || message.includes('room')) tags.push('room');
    if (title.includes('spa') || message.includes('spa')) tags.push('spa');
    if (title.includes('pool') || message.includes('pool')) tags.push('pool');
    if (title.includes('wifi') || message.includes('wifi')) tags.push('wifi');
    if (title.includes('offer') || message.includes('offer')) tags.push('offer');

    return tags;
  };

  // Enhanced action handlers
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      // Simulate refresh delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Regenerate mock notifications
      const mockNotifications = generateMockNotifications();
      setNotifications(mockNotifications);

      toast.success('Notifications refreshed');
    } catch (error) {
      toast.error('Failed to refresh notifications');
    } finally {
      setIsRefreshing(false);
    }
  }, [generateMockNotifications]);

  const handleBulkAction = useCallback(async (action: 'read' | 'delete' | 'archive') => {
    if (selectedNotifications.size === 0) return;

    setLoading(true);
    try {
      const notificationIds = Array.from(selectedNotifications);

      switch (action) {
        case 'read':
          setNotifications(prev => prev.map(n =>
            notificationIds.includes(n.id) ? { ...n, isRead: true } : n
          ));
          toast.success(`Marked ${notificationIds.length} notifications as read`);
          break;
        case 'delete':
          setNotifications(prev => prev.filter(n => !notificationIds.includes(n.id)));
          toast.success(`Deleted ${notificationIds.length} notifications`);
          break;
        case 'archive':
          setNotifications(prev => prev.map(n =>
            notificationIds.includes(n.id) ? { ...n, isArchived: true } : n
          ));
          toast.success(`Archived ${notificationIds.length} notifications`);
          break;
      }

      setSelectedNotifications(new Set());
    } catch (error) {
      toast.error(`Failed to ${action} notifications`);
    } finally {
      setLoading(false);
    }
  }, [selectedNotifications]);

  const toggleNotificationSelection = useCallback((notificationId: string) => {
    setSelectedNotifications(prev => {
      const newSet = new Set(prev);
      if (newSet.has(notificationId)) {
        newSet.delete(notificationId);
      } else {
        newSet.add(notificationId);
      }
      return newSet;
    });
  }, []);

  const toggleStarred = useCallback((notificationId: string) => {
    setNotifications(prev => prev.map(n =>
      n.id === notificationId ? { ...n, isStarred: !n.isStarred } : n
    ));
  }, []);

  // Enhanced notification click handler with navigation
  const handleNotificationClick = useCallback(async (notification: EnhancedNotification) => {
    // Mark as read first
    if (!notification.isRead) {
      await handleMarkAsRead(notification.id);
    }

    // Close dropdown
    setIsOpen(false);

    // Navigate based on notification type and actionUrl
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    } else {
      // Default navigation based on category
      switch (notification.category) {
        case 'bookings':
          navigate('/bookings');
          break;
        case 'amenities':
          navigate('/#amenities');
          // Scroll to amenities section
          setTimeout(() => {
            const element = document.getElementById('amenities');
            if (element) {
              element.scrollIntoView({ behavior: 'smooth' });
            }
          }, 100);
          break;
        case 'offers':
          navigate('/offers');
          break;
        case 'alerts':
          // Show detailed alert modal or navigate to alerts page
          console.log('Alert notification clicked:', notification);
          break;
        case 'system':
          navigate('/settings');
          break;
        default:
          console.log('Notification clicked:', notification);
      }
    }

    // Show success toast
    toast.success('Notification opened');
  }, [navigate, handleMarkAsRead]);

  // Open full notification center
  const openViewAll = useCallback(() => {
    setIsViewAllOpen(true);
    setIsOpen(false);
  }, []);

  // Enhanced notification action handlers
  const handleQuickAction = useCallback((action: string, notificationId: string) => {
    const notification = notifications.find(n => n.id === notificationId);
    if (!notification) return;

    switch (action) {
      case 'accept':
        toast.success('Booking request accepted');
        handleMarkAsRead(notificationId);
        break;
      case 'decline':
        toast.info('Booking request declined');
        handleMarkAsRead(notificationId);
        break;
      case 'view':
        handleNotificationClick(notification);
        break;
      case 'dismiss':
        handleMarkAsRead(notificationId);
        break;
      default:
        console.log('Unknown action:', action);
    }
  }, [notifications, handleMarkAsRead, handleNotificationClick]);

  const handleMarkAsRead = useCallback(async (notificationId: string) => {
    try {
      setLoading(true);
      await markNotificationAsRead(notificationId);
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
      );
      toast.success('Notification marked as read');
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
    } finally {
      setLoading(false);
    }
  }, []);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'booking_confirmed':
        return <Calendar className="w-4 h-4 text-green-500" />;
      case 'booking_cancelled':
        return <X className="w-4 h-4 text-red-500" />;
      case 'room_availability':
        return <Hotel className="w-4 h-4 text-blue-500" />;
      case 'admin_message':
        return <MessageSquare className="w-4 h-4 text-purple-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  // Enhanced utility functions
  const handleMarkAllAsRead = async () => {
    setLoading(true);
    try {
      const unreadNotifications = filteredNotifications().filter(n => !n.isRead);
      await Promise.all(
        unreadNotifications.map(n => markNotificationAsRead(n.id))
      );
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      toast.success('All notifications marked as read');
    } catch (error) {
      toast.error('Failed to mark all notifications as read');
    } finally {
      setLoading(false);
    }
  };

  const getCategoryIcon = (category?: NotificationCategory) => {
    switch (category) {
      case 'bookings': return Calendar;
      case 'amenities': return Hotel;
      case 'offers': return Gift;
      case 'alerts': return AlertCircle;
      case 'system': return Settings;
      default: return Bell;
    }
  };

  const getPriorityColor = (priority?: NotificationPriority) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'low': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const filtered = filteredNotifications();
  const unreadCount = notifications.filter(n => !n.isRead).length;
  const selectedCount = selectedNotifications.size;

  if (!firebaseUser) return null;

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="w-5 h-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-80 max-h-96 overflow-y-auto">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          {unreadCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {unreadCount} new
            </Badge>
          )}
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        {notifications.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No notifications</p>
          </div>
        ) : (
          <div className="max-h-80 overflow-y-auto">
            {notifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className="flex items-start gap-3 p-3 cursor-pointer hover:bg-muted/50"
                onClick={() => handleMarkAsRead(notification.id)}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {getNotificationIcon(notification.type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <h4 className="text-sm font-medium truncate">
                      {notification.title}
                    </h4>
                    <span className="text-xs text-muted-foreground whitespace-nowrap">
                      {formatTimeAgo(notification.createdAt)}
                    </span>
                  </div>
                  
                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {notification.message}
                  </p>
                  
                  {!notification.isRead && (
                    <div className="flex items-center gap-2 mt-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 px-2 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMarkAsRead(notification.id);
                        }}
                        disabled={loading}
                      >
                        <Check className="w-3 h-3 mr-1" />
                        Mark as read
                      </Button>
                    </div>
                  )}
                </div>
              </DropdownMenuItem>
            ))}
          </div>
        )}
        
        {/* Enhanced Footer Actions */}
        {notifications.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <div className="p-3 bg-gray-50 space-y-2">
              {/* Quick Actions Row */}
              <div className="flex items-center justify-between gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleMarkAllAsRead}
                  disabled={loading || unreadCount === 0}
                  className="flex-1 text-xs h-8"
                >
                  <Check className="w-3 h-3 mr-1" />
                  Mark all read ({unreadCount})
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className={cn("w-3 h-3", isRefreshing && "animate-spin")} />
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSoundEnabled(!soundEnabled)}
                  className="h-8 w-8 p-0"
                >
                  {soundEnabled ? (
                    <Volume2 className="w-3 h-3" />
                  ) : (
                    <VolumeX className="w-3 h-3" />
                  )}
                </Button>
              </div>

              {/* Category Filter Pills */}
              <div className="flex flex-wrap gap-1">
                {categories.slice(0, 4).map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                    className="text-xs h-6 px-2"
                  >
                    <category.icon className="w-3 h-3 mr-1" />
                    {category.label}
                    {category.count > 0 && (
                      <Badge variant="secondary" className="ml-1 text-xs px-1 py-0 h-4">
                        {category.count}
                      </Badge>
                    )}
                  </Button>
                ))}
              </div>

              {/* View All Button */}
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="w-full text-xs h-8">
                    <Maximize2 className="w-3 h-3 mr-1" />
                    View All Notifications ({notifications.length})
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      <Bell className="w-5 h-5" />
                      All Notifications
                    </DialogTitle>
                    <DialogDescription>
                      Comprehensive notification management center with advanced features
                    </DialogDescription>
                  </DialogHeader>

                  {/* Enhanced Modal Content */}
                  <div className="space-y-4">
                    {/* Search and Filters */}
                    <div className="flex items-center gap-3">
                      <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <Input
                          placeholder="Search notifications..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10"
                        />
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowOnlyUnread(!showOnlyUnread)}
                        className={cn(
                          "whitespace-nowrap",
                          showOnlyUnread && "bg-primary text-white"
                        )}
                      >
                        <Filter className="w-4 h-4 mr-1" />
                        {showOnlyUnread ? "Show All" : "Unread Only"}
                      </Button>
                    </div>

                    {/* Category Tabs */}
                    <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as NotificationCategory)}>
                      <TabsList className="grid w-full grid-cols-6">
                        {categories.map((category) => (
                          <TabsTrigger key={category.id} value={category.id} className="text-xs">
                            <category.icon className="w-3 h-3 mr-1" />
                            {category.label}
                            {category.count > 0 && (
                              <Badge variant="secondary" className="ml-1 text-xs px-1 py-0 h-4">
                                {category.count}
                              </Badge>
                            )}
                          </TabsTrigger>
                        ))}
                      </TabsList>

                      {/* Tab Content */}
                      <div className="mt-4 max-h-96 overflow-y-auto border rounded-lg">
                        {filtered.length === 0 ? (
                          <div className="p-8 text-center text-gray-500">
                            <Bell className="w-12 h-12 mx-auto mb-4 opacity-30" />
                            <p className="text-sm font-medium">No notifications found</p>
                            <p className="text-xs text-gray-400 mt-1">
                              {searchQuery
                                ? `No results for "${searchQuery}"`
                                : selectedCategory === 'all'
                                  ? "You're all caught up!"
                                  : `No ${selectedCategory} notifications`
                              }
                            </p>
                          </div>
                        ) : (
                          <div className="divide-y divide-gray-100">
                            {filtered.map((notification, index) => (
                              <div
                                key={notification.id}
                                className={cn(
                                  "p-4 hover:bg-gray-50 transition-colors",
                                  !notification.isRead && "bg-blue-50/50 border-l-4 border-l-primary"
                                )}
                              >
                                <div className="flex items-start gap-3">
                                  <div className={cn(
                                    "p-2 rounded-lg",
                                    getPriorityColor(notification.priority)
                                  )}>
                                    {React.createElement(getCategoryIcon(notification.category), {
                                      className: "w-4 h-4"
                                    })}
                                  </div>

                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-start justify-between">
                                      <h4 className="text-sm font-semibold text-gray-900">
                                        {notification.title}
                                      </h4>
                                      <div className="flex items-center gap-2">
                                        {notification.priority === 'urgent' && (
                                          <Badge variant="destructive" className="text-xs">
                                            Urgent
                                          </Badge>
                                        )}
                                        <span className="text-xs text-gray-400">
                                          {formatTimeAgo(notification.createdAt)}
                                        </span>
                                      </div>
                                    </div>

                                    <p className="text-sm text-gray-600 mt-1">
                                      {notification.message}
                                    </p>

                                    {notification.tags && notification.tags.length > 0 && (
                                      <div className="flex flex-wrap gap-1 mt-2">
                                        {notification.tags.map((tag, tagIndex) => (
                                          <Badge key={tagIndex} variant="outline" className="text-xs">
                                            {tag}
                                          </Badge>
                                        ))}
                                      </div>
                                    )}

                                    <div className="flex items-center justify-between mt-3">
                                      <div className="flex items-center gap-2">
                                        {!notification.isRead && (
                                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                                        )}
                                        <span className="text-xs text-gray-500">
                                          {notification.category}
                                        </span>
                                      </div>

                                      <div className="flex items-center gap-1">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => toggleStarred(notification.id)}
                                          className="h-6 w-6 p-0"
                                        >
                                          <Star className={cn(
                                            "w-3 h-3",
                                            notification.isStarred ? "text-yellow-500 fill-current" : "text-gray-400"
                                          )} />
                                        </Button>

                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleMarkAsRead(notification.id)}
                                          className="h-6 w-6 p-0"
                                        >
                                          <Check className="w-3 h-3 text-gray-400" />
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </Tabs>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </>
        )}
      </DropdownMenuContent>
      </DropdownMenu>

      {/* Enhanced View All Notifications Modal */}
    <Dialog open={isViewAllOpen} onOpenChange={setIsViewAllOpen}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden p-0">
        <div className="flex flex-col h-full">
          {/* Modal Header */}
          <DialogHeader className="p-6 border-b border-gray-200 bg-gradient-to-r from-primary/5 to-primary/10">
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Bell className="w-6 h-6 text-primary" />
                  </div>
                  All Notifications
                  <Badge variant="secondary" className="ml-2">
                    {filtered.length}
                  </Badge>
                </DialogTitle>
                <DialogDescription className="mt-2">
                  Comprehensive notification management center with advanced features
                </DialogDescription>
              </div>

              {/* Header Actions */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="h-9"
                >
                  <RefreshCw className={cn("w-4 h-4 mr-2", isRefreshing && "animate-spin")} />
                  Refresh
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSoundEnabled(!soundEnabled)}
                  className="h-9 w-9 p-0"
                >
                  {soundEnabled ? (
                    <Volume2 className="w-4 h-4" />
                  ) : (
                    <VolumeX className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>
          </DialogHeader>

          {/* Search and Filters */}
          <div className="p-6 border-b border-gray-200 bg-gray-50/50">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search Bar */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search notifications by title, message, or tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-10"
                />
              </div>

              {/* Filter Controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowOnlyUnread(!showOnlyUnread)}
                  className={cn(
                    "whitespace-nowrap h-10",
                    showOnlyUnread && "bg-primary text-white"
                  )}
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {showOnlyUnread ? "Show All" : "Unread Only"}
                </Button>

                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'date' | 'priority' | 'category')}
                  className="h-10 px-3 border border-gray-300 rounded-md text-sm bg-white"
                >
                  <option value="date">Sort by Date</option>
                  <option value="priority">Sort by Priority</option>
                  <option value="category">Sort by Category</option>
                </select>
              </div>
            </div>

            {/* Category Tabs - Responsive */}
            <div className="mt-4">
              <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as NotificationCategory)}>
                <TabsList className="grid w-full grid-cols-3 sm:grid-cols-6 h-auto">
                  {categories.map((category) => (
                    <TabsTrigger
                      key={category.id}
                      value={category.id}
                      className="text-xs sm:text-sm p-2 sm:p-3 flex flex-col sm:flex-row items-center gap-1"
                    >
                      <category.icon className="w-4 h-4" />
                      <span className="hidden sm:inline">{category.label}</span>
                      <span className="sm:hidden">{category.label.slice(0, 3)}</span>
                      {category.count > 0 && (
                        <Badge variant="secondary" className="text-xs px-1 py-0 h-4 min-w-[16px]">
                          {category.count}
                        </Badge>
                      )}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>

            {/* Bulk Actions */}
            {selectedCount > 0 && (
              <div className="mt-4 p-3 bg-primary/5 rounded-lg border border-primary/20">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-primary">
                    {selectedCount} notification{selectedCount > 1 ? 's' : ''} selected
                  </span>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBulkAction('read')}
                      disabled={loading}
                      className="h-8"
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      Mark Read
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBulkAction('archive')}
                      disabled={loading}
                      className="h-8"
                    >
                      <Archive className="w-3 h-3 mr-1" />
                      Archive
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBulkAction('delete')}
                      disabled={loading}
                      className="h-8 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-3 h-3 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Notifications List - Responsive */}
          <div className="flex-1 overflow-y-auto">
            {filtered.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <Bell className="w-16 h-16 mb-4 opacity-30" />
                <h3 className="text-lg font-medium mb-2">No notifications found</h3>
                <p className="text-sm text-center max-w-md">
                  {searchQuery
                    ? `No results found for "${searchQuery}". Try adjusting your search terms.`
                    : selectedCategory === 'all'
                      ? "You're all caught up! We'll notify you when something important happens."
                      : `No ${selectedCategory} notifications at the moment.`
                  }
                </p>
                {searchQuery && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSearchQuery('')}
                    className="mt-4"
                  >
                    Clear Search
                  </Button>
                )}
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {filtered.map((notification) => (
                  <div
                    key={notification.id}
                    className={cn(
                      "group p-4 sm:p-6 hover:bg-gray-50 transition-all duration-200 cursor-pointer border-l-4 border-transparent",
                      !notification.isRead && "bg-blue-50/50 border-l-primary",
                      selectedNotifications.has(notification.id) && "bg-primary/10"
                    )}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start gap-3 sm:gap-4">
                      {/* Selection Checkbox */}
                      <input
                        type="checkbox"
                        checked={selectedNotifications.has(notification.id)}
                        onChange={(e) => {
                          e.stopPropagation();
                          toggleNotificationSelection(notification.id);
                        }}
                        className="mt-1 rounded border-gray-300 text-primary focus:ring-primary"
                      />

                      {/* Category Icon */}
                      <div className={cn(
                        "flex-shrink-0 p-3 rounded-xl",
                        getPriorityColor(notification.priority)
                      )}>
                        {React.createElement(getCategoryIcon(notification.category), {
                          className: "w-5 h-5"
                        })}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="text-base font-semibold text-gray-900 pr-4">
                            {notification.title}
                          </h4>
                          <div className="flex items-center gap-2 flex-shrink-0">
                            {notification.priority === 'urgent' && (
                              <Badge variant="destructive" className="text-xs">
                                Urgent
                              </Badge>
                            )}
                            {notification.priority === 'high' && (
                              <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-700">
                                High
                              </Badge>
                            )}
                            {notification.isStarred && (
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            )}
                            {!notification.isRead && (
                              <div className="w-3 h-3 bg-primary rounded-full"></div>
                            )}
                          </div>
                        </div>

                        <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                          {notification.message}
                        </p>

                        {/* Tags */}
                        {notification.tags && notification.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-3">
                            {notification.tags.map((tag, tagIndex) => (
                              <Badge key={tagIndex} variant="outline" className="text-xs">
                                <Tag className="w-3 h-3 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}

                        {/* Footer */}
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {formatTimeAgo(notification.createdAt)}
                            </span>
                            <span className="flex items-center gap-1">
                              {React.createElement(getCategoryIcon(notification.category), {
                                className: "w-3 h-3"
                              })}
                              {notification.category}
                            </span>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            {notification.category === 'bookings' && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleQuickAction('accept', notification.id);
                                  }}
                                  className="h-7 px-3 text-xs text-green-600 border-green-200 hover:bg-green-50"
                                >
                                  Accept
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleQuickAction('decline', notification.id);
                                  }}
                                  className="h-7 px-3 text-xs text-red-600 border-red-200 hover:bg-red-50"
                                >
                                  Decline
                                </Button>
                              </>
                            )}

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleStarred(notification.id);
                              }}
                              className="h-7 w-7 p-0"
                            >
                              <Star className={cn(
                                "w-4 h-4",
                                notification.isStarred ? "text-yellow-500 fill-current" : "text-gray-400"
                              )} />
                            </Button>

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleMarkAsRead(notification.id);
                              }}
                              className="h-7 w-7 p-0"
                            >
                              {notification.isRead ? (
                                <EyeOff className="w-4 h-4 text-gray-400" />
                              ) : (
                                <Eye className="w-4 h-4 text-gray-400" />
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Modal Footer */}
          <div className="p-4 border-t border-gray-200 bg-gray-50/50">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-sm text-gray-600">
                Showing {filtered.length} of {notifications.length} notifications
                {unreadCount > 0 && (
                  <span className="ml-2 text-primary font-medium">
                    • {unreadCount} unread
                  </span>
                )}
              </div>

              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    disabled={loading}
                  >
                    <Check className="w-4 h-4 mr-2" />
                    Mark All Read
                  </Button>
                )}

                <Button
                  variant="outline"
                  onClick={() => setIsViewAllOpen(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
    </>
  );
};

export default NotificationCenter;
