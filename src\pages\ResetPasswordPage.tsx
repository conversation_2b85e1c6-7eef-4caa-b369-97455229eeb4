// Password Reset Completion Page - Handle Email Link Clicks
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useSearchPara<PERSON>, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Lock,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle,
  Loader2,
  Key,
  Shield,
  ArrowLeft,
  X,
  Check,
  Building
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import {
  verifyPasswordResetCode,
  confirmPasswordReset
} from 'firebase/auth';
import { auth } from '@/lib/firebase';

// Password validation function
const validatePasswordStrength = (password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} => {
  const feedback: string[] = [];
  let score = 0;

  // Length check
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Password must be at least 8 characters long');
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one uppercase letter');
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one lowercase letter');
  }

  // Number check
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one number');
  }

  // Special character check
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one special character');
  }

  return {
    isValid: score >= 3,
    score,
    feedback
  };
};

// Generate password suggestions
const generatePasswordSuggestions = (): string[] => {
  const adjectives = ['Secure', 'Strong', 'Safe', 'Protected', 'Guarded'];
  const nouns = ['Hotel', 'Stay', 'Room', 'Guest', 'Booking'];
  const symbols = ['!', '@', '#', '$', '%'];

  const suggestions = [];

  for (let i = 0; i < 3; i++) {
    const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const symbol = symbols[Math.floor(Math.random() * symbols.length)];
    const num = Math.floor(Math.random() * 99) + 10;

    suggestions.push(`${adj}${noun}${num}${symbol}`);
  }

  return suggestions;
};

const ResetPasswordPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(true);
  const [codeValid, setCodeValid] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [passwordStrength, setPasswordStrength] = useState({ isValid: false, score: 0, feedback: [] });
  const [resetComplete, setResetComplete] = useState(false);
  const passwordInputRef = useRef<HTMLInputElement>(null);

  const code = searchParams.get('oobCode') || '';
  const mode = searchParams.get('mode') || '';

  // Verify the reset code on component mount
  useEffect(() => {
    const verifyCode = async () => {
      if (!code || mode !== 'resetPassword') {
        setError('Invalid or missing reset code');
        setVerifying(false);
        return;
      }

      try {
        const email = await verifyPasswordResetCode(auth, code);
        setCodeValid(true);
        setEmail(email);
        setTimeout(() => passwordInputRef.current?.focus(), 100);
      } catch (error: any) {
        console.error('Password reset code verification error:', error);

        let errorMessage = 'Invalid or expired reset code';

        switch (error.code) {
          case 'auth/expired-action-code':
            errorMessage = 'Password reset link has expired. Please request a new one';
            break;
          case 'auth/invalid-action-code':
            errorMessage = 'Invalid password reset link. Please request a new one';
            break;
          case 'auth/user-disabled':
            errorMessage = 'This account has been disabled';
            break;
          case 'auth/user-not-found':
            errorMessage = 'No account found for this reset request';
            break;
          default:
            errorMessage = error.message || 'Invalid or expired reset code';
        }

        setError(errorMessage);
      } finally {
        setVerifying(false);
      }
    };

    verifyCode();
  }, [code, mode]);

  // Update password strength as user types
  useEffect(() => {
    if (password) {
      const strength = validatePasswordStrength(password);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength({ isValid: false, score: 0, feedback: [] });
    }
  }, [password]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!password) {
      setError('Password is required');
      return;
    }

    if (!passwordStrength.isValid) {
      setError('Please choose a stronger password');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await confirmPasswordReset(auth, code, password);
      setResetComplete(true);
      toast.success('Password reset successfully!');

      // Redirect to sign in after 3 seconds
      setTimeout(() => {
        navigate('/signin', {
          state: { message: 'Password reset successfully. Please sign in with your new password.' }
        });
      }, 3000);
    } catch (error: any) {
      console.error('Password reset confirmation error:', error);

      let errorMessage = 'Failed to reset password';

      switch (error.code) {
        case 'auth/expired-action-code':
          errorMessage = 'Password reset link has expired. Please request a new one';
          break;
        case 'auth/invalid-action-code':
          errorMessage = 'Invalid password reset link. Please request a new one';
          break;
        case 'auth/weak-password':
          errorMessage = 'Password is too weak. Please choose a stronger password';
          break;
        case 'auth/user-disabled':
          errorMessage = 'This account has been disabled';
          break;
        case 'auth/user-not-found':
          errorMessage = 'No account found for this reset request';
          break;
        default:
          errorMessage = error.message || 'Failed to reset password';
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle close
  const handleClose = () => {
    navigate('/signin');
  };

  // Get password strength color
  const getStrengthColor = (score: number) => {
    if (score <= 1) return 'bg-red-500';
    if (score <= 2) return 'bg-orange-500';
    if (score <= 3) return 'bg-yellow-500';
    if (score <= 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  // Get password strength label
  const getStrengthLabel = (score: number) => {
    if (score <= 1) return 'Very Weak';
    if (score <= 2) return 'Weak';
    if (score <= 3) return 'Fair';
    if (score <= 4) return 'Good';
    return 'Strong';
  };

  // Generate password suggestions
  const suggestions = generatePasswordSuggestions();

  if (verifying) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center pt-20">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-primary mb-4" />
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Verifying Reset Link</h2>
            <p className="text-sm text-gray-600 text-center">
              Please wait while we verify your password reset link...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!codeValid) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center pt-20">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Invalid Reset Link</h2>
            <p className="text-sm text-gray-600 text-center mb-6">
              {error || 'This password reset link is invalid or has expired.'}
            </p>
            <div className="flex gap-3 w-full">
              <Button variant="outline" onClick={() => navigate('/forgot-password')} className="flex-1">
                Request New Link
              </Button>
              <Button onClick={() => navigate('/signin')} className="flex-1">
                Back to Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (resetComplete) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center pt-20">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Password Reset Complete!</h2>
            <p className="text-sm text-gray-600 text-center mb-6">
              Your password has been successfully reset. You can now sign in with your new password.
            </p>
            <Button onClick={() => navigate('/signin')} className="w-full">
              Continue to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 pt-20">
      {/* Close Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClose}
        className="fixed top-20 right-4 z-50 rounded-full w-10 h-10 p-0 hover:bg-red-50 hover:text-red-600 transition-all duration-200 group bg-white/90 backdrop-blur-sm shadow-lg"
        title="Close"
      >
        <X className="w-5 h-5 text-gray-500 group-hover:text-red-600 transition-colors" />
      </Button>

      <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[calc(100vh-5rem)]">
        <Card className="w-full max-w-lg shadow-xl border-0 bg-white/90 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <div className="mx-auto mb-4 p-4 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full w-fit">
              <Lock className="w-8 h-8 text-primary" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              Create New Password
            </CardTitle>
            <p className="text-gray-600 mt-2">
              Enter a strong password for your account: {email}
            </p>
          </CardHeader>

          <CardContent className="space-y-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* New Password */}
              <div className="space-y-2">
                <Label htmlFor="password">New Password</Label>
                <div className="relative">
                  <Input
                    ref={passwordInputRef}
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      setError('');
                    }}
                    placeholder="Enter your new password"
                    className={cn("pl-10 pr-10", error && "border-red-500")}
                    disabled={loading}
                  />
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>

                {/* Password Strength Indicator */}
                {password && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-600">Password Strength:</span>
                      <span className={cn(
                        "text-xs font-medium",
                        passwordStrength.score <= 2 ? "text-red-600" : 
                        passwordStrength.score <= 3 ? "text-yellow-600" : "text-green-600"
                      )}>
                        {getStrengthLabel(passwordStrength.score)}
                      </span>
                    </div>
                    <Progress 
                      value={(passwordStrength.score / 5) * 100} 
                      className="h-2"
                    />
                    {passwordStrength.feedback.length > 0 && (
                      <ul className="text-xs text-gray-600 space-y-1">
                        {passwordStrength.feedback.map((feedback, index) => (
                          <li key={index} className="flex items-center gap-1">
                            <AlertCircle className="w-3 h-3 text-orange-500" />
                            {feedback}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                )}
              </div>

              {/* Confirm Password */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => {
                      setConfirmPassword(e.target.value);
                      setError('');
                    }}
                    placeholder="Confirm your new password"
                    className={cn("pl-10 pr-10", error && "border-red-500")}
                    disabled={loading}
                  />
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
                
                {/* Password Match Indicator */}
                {confirmPassword && (
                  <div className="flex items-center gap-1 text-xs">
                    {password === confirmPassword ? (
                      <>
                        <Check className="w-3 h-3 text-green-600" />
                        <span className="text-green-600">Passwords match</span>
                      </>
                    ) : (
                      <>
                        <AlertCircle className="w-3 h-3 text-red-600" />
                        <span className="text-red-600">Passwords do not match</span>
                      </>
                    )}
                  </div>
                )}
              </div>

              {/* Error Message */}
              {error && (
                <div className="flex items-center gap-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg p-3">
                  <AlertCircle className="w-4 h-4 flex-shrink-0" />
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <Button 
                type="submit" 
                disabled={loading || !passwordStrength.isValid || password !== confirmPassword}
                className="w-full h-12 text-base font-semibold"
                size="lg"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Resetting Password...
                  </>
                ) : (
                  <>
                    Reset Password
                    <Key className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </form>

            {/* Password Suggestions */}
            {!password && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Password Suggestions:</h4>
                <div className="space-y-1">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => setPassword(suggestion)}
                      className="block w-full text-left text-xs text-blue-700 hover:text-blue-900 font-mono bg-blue-100 hover:bg-blue-200 rounded px-2 py-1 transition-colors"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
                <p className="text-xs text-blue-600 mt-2">Click any suggestion to use it as your password</p>
              </div>
            )}

            {/* Back to Sign In */}
            <div className="text-center">
              <Link to="/signin" className="flex items-center justify-center gap-2 text-sm text-primary hover:underline">
                <ArrowLeft className="w-4 h-4" />
                Back to Sign In
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
