// User booking history component
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { collection, query, where, orderBy, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { getHotel, getRoom } from '@/services/hotelService';
import { Booking, Hotel, Room } from '@/types/hotel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  MapPin, 
  Users, 
  IndianRupee, 
  Clock,
  Phone,
  Mail,
  FileText,
  Star,
  Download
} from 'lucide-react';
import { toast } from 'sonner';

interface BookingWithDetails extends Booking {
  hotel?: Hotel;
  room?: Room;
}

interface BookingHistoryProps {
  className?: string;
}

const BookingHistory: React.FC<BookingHistoryProps> = ({ className }) => {
  const { user, firebaseUser } = useAuth();
  const [bookings, setBookings] = useState<BookingWithDetails[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (firebaseUser) {
      loadBookingHistory();
    }
  }, [firebaseUser]);

  const loadBookingHistory = async () => {
    if (!firebaseUser) return;

    try {
      setLoading(true);
      
      // Query bookings for the current user
      const q = query(
        collection(db, 'bookings'),
        where('userId', '==', firebaseUser.uid),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const bookingsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Booking[];

      // Load hotel and room details for each booking
      const bookingsWithDetails = await Promise.all(
        bookingsData.map(async (booking) => {
          try {
            const [hotel, room] = await Promise.all([
              getHotel(booking.hotelId),
              getRoom(booking.roomId)
            ]);
            
            return {
              ...booking,
              hotel: hotel || undefined,
              room: room || undefined
            };
          } catch (error) {
            console.error('Error loading booking details:', error);
            return booking;
          }
        })
      );

      setBookings(bookingsWithDetails);
    } catch (error) {
      console.error('Error loading booking history:', error);
      toast.error('Failed to load booking history');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      case 'completed':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'text-green-600';
      case 'pending':
        return 'text-yellow-600';
      case 'cancelled':
        return 'text-red-600';
      case 'completed':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateNights = (checkIn: Date, checkOut: Date) => {
    const diffTime = Math.abs(new Date(checkOut).getTime() - new Date(checkIn).getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const handleDownloadReceipt = (bookingId: string) => {
    // This would generate and download a PDF receipt
    toast.info('Receipt download feature coming soon!');
  };

  const handleCancelBooking = (bookingId: string) => {
    // This would handle booking cancellation
    toast.info('Booking cancellation feature coming soon!');
  };

  if (!firebaseUser) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-12">
          <Calendar className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-xl font-semibold mb-2">Sign In Required</h3>
          <p className="text-muted-foreground">
            Please sign in to view your booking history.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h2 className="text-2xl font-bold mb-2">My Bookings</h2>
        <p className="text-muted-foreground">
          View and manage your hotel reservations
        </p>
      </div>

      {bookings.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Calendar className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-xl font-semibold mb-2">No Bookings Yet</h3>
            <p className="text-muted-foreground mb-4">
              You haven't made any bookings yet. Start exploring our hotels!
            </p>
            <Button onClick={() => window.location.href = '/#booking'}>
              Book Your Stay
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {bookings.map((booking) => (
            <Card key={booking.id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="grid md:grid-cols-3 gap-0">
                  {/* Hotel Image */}
                  <div className="relative h-48 md:h-auto">
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      {booking.hotel?.images && booking.hotel.images.length > 0 ? (
                        <img 
                          src={booking.hotel.images[0]} 
                          alt={booking.hotel.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <MapPin className="w-12 h-12 mb-2" />
                          <span className="text-sm">Hotel Image</span>
                        </div>
                      )}
                    </div>
                    
                    <Badge 
                      variant={getStatusBadgeVariant(booking.status)} 
                      className="absolute top-4 right-4"
                    >
                      {booking.status}
                    </Badge>
                  </div>

                  {/* Booking Details */}
                  <div className="md:col-span-2 p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-xl font-semibold mb-1">
                          {booking.hotel?.name || 'Hotel Name'}
                        </h3>
                        <p className="text-sm text-muted-foreground mb-2">
                          {booking.room?.name || 'Room'} • {booking.room?.type}
                        </p>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <MapPin className="w-4 h-4" />
                          <span>{booking.hotel?.address || 'Tiruvannamalai'}</span>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-2xl font-bold text-primary">
                          ₹{booking.totalAmount.toLocaleString()}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {calculateNights(booking.checkInDate, booking.checkOutDate)} night{calculateNights(booking.checkInDate, booking.checkOutDate) !== 1 ? 's' : ''}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Check-in</div>
                          <div className="text-muted-foreground">
                            {formatDate(booking.checkInDate)}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Check-out</div>
                          <div className="text-muted-foreground">
                            {formatDate(booking.checkOutDate)}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Guests</div>
                          <div className="text-muted-foreground">
                            {booking.numberOfGuests} guest{booking.numberOfGuests !== 1 ? 's' : ''}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Booked</div>
                          <div className="text-muted-foreground">
                            {new Date(booking.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Guest Information */}
                    <div className="bg-muted/50 rounded-lg p-3 mb-4">
                      <h4 className="font-medium mb-2 text-sm">Guest Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-muted-foreground" />
                          <span>{booking.guestInfo.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4 text-muted-foreground" />
                          <span>{booking.guestInfo.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-muted-foreground" />
                          <span>{booking.guestInfo.phone}</span>
                        </div>
                      </div>
                    </div>

                    {/* Special Requests */}
                    {booking.specialRequests && (
                      <div className="mb-4">
                        <div className="flex items-center gap-2 text-sm">
                          <FileText className="w-4 h-4 text-muted-foreground" />
                          <span className="font-medium">Special Requests:</span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1 pl-6">
                          {booking.specialRequests}
                        </p>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-4 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadReceipt(booking.id)}
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Receipt
                      </Button>
                      
                      {booking.hotel?.phone && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`tel:${booking.hotel?.phone}`)}
                        >
                          <Phone className="w-4 h-4 mr-2" />
                          Call Hotel
                        </Button>
                      )}
                      
                      {booking.status === 'confirmed' && new Date(booking.checkInDate) > new Date() && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCancelBooking(booking.id)}
                        >
                          Cancel Booking
                        </Button>
                      )}
                      
                      {booking.status === 'completed' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toast.info('Review feature coming soon!')}
                        >
                          <Star className="w-4 h-4 mr-2" />
                          Write Review
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default BookingHistory;
