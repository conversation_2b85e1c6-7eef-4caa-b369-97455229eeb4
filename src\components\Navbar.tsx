// Enhanced Navbar component with modern design, touch support, and scrolling features
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { NavLink, useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Hotel, 
  Home, 
  Search, 
  Building, 
  BarChart3, 
  Bed, 
  Calendar, 
  Smartphone, 
  User,
  LogOut,
  X,
  Phone,
  Mail,
  Shield,
  Menu,
  ChevronLeft,
  ChevronRight,
  ArrowUp
} from 'lucide-react';
import StaticNotificationCenter from './StaticNotificationCenter';
import { signOutUser } from '@/services/authService';
import { toast } from 'sonner';

interface NavbarProps {
  className?: string;
}

const Navbar: React.FC<NavbarProps> = ({ className }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [navScrollPosition, setNavScrollPosition] = useState(0);
  const [isNavScrollable, setIsNavScrollable] = useState(false);
  
  const { user, admin, firebaseUser } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const navScrollRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // Helper function to check if a route is active
  const isRouteActive = useCallback((path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  }, [location.pathname]);

  // Enhanced scroll detection with throttling
  useEffect(() => {
    let ticking = false;
    
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrollY = window.scrollY;
          setIsScrolled(scrollY > 20);
          setShowScrollTop(scrollY > 300);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Check if navigation is scrollable
  useEffect(() => {
    const checkScrollable = () => {
      if (navScrollRef.current) {
        const { scrollWidth, clientWidth } = navScrollRef.current;
        setIsNavScrollable(scrollWidth > clientWidth);
      }
    };

    checkScrollable();
    window.addEventListener('resize', checkScrollable);
    return () => window.removeEventListener('resize', checkScrollable);
  }, []);

  // Enhanced scroll to section with offset for fixed navbar
  const scrollToSection = useCallback((sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const navbarHeight = 80;
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - navbarHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
    setIsMobileMenuOpen(false);
  }, []);

  // Scroll to top function
  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }, []);

  // Navigation scroll functions
  const scrollNavLeft = useCallback(() => {
    if (navScrollRef.current) {
      const scrollAmount = 200;
      navScrollRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
    }
  }, []);

  const scrollNavRight = useCallback(() => {
    if (navScrollRef.current) {
      const scrollAmount = 200;
      navScrollRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  }, []);

  // Touch event handlers for mobile menu
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    const startX = touch.clientX;
    
    const handleTouchMove = (moveEvent: TouchEvent) => {
      const currentTouch = moveEvent.touches[0];
      const deltaX = currentTouch.clientX - startX;
      
      if (isMobileMenuOpen && deltaX > 100) {
        setIsMobileMenuOpen(false);
      }
    };
    
    const handleTouchEnd = () => {
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
    
    document.addEventListener('touchmove', handleTouchMove, { passive: true });
    document.addEventListener('touchend', handleTouchEnd);
  }, [isMobileMenuOpen]);

  // Enhanced mobile menu toggle with haptic feedback
  const toggleMobileMenu = useCallback(() => {
    setIsMobileMenuOpen(prev => {
      const newState = !prev;
      
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
      
      if (newState) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }
      
      return newState;
    });
  }, []);

  // Close mobile menu on escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isMobileMenuOpen]);

  // Clean up body scroll on unmount
  useEffect(() => {
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const handleLogout = async () => {
    try {
      await signOutUser();
      toast.success('Logged out successfully');
      navigate('/');
      setIsMobileMenuOpen(false);
    } catch (error) {
      toast.error('Failed to logout');
    }
  };

  // Navigate to dedicated auth pages instead of modal
  const navigateToAuth = (type: 'signin' | 'signup' | 'admin') => {
    setIsMobileMenuOpen(false);

    switch (type) {
      case 'signin':
        navigate('/signin');
        break;
      case 'signup':
        navigate('/signup');
        break;
      case 'admin':
        navigate('/admin/login');
        break;
    }
  };



  // Navigation items with proper page routing
  const navigationItems = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      path: '/',
      action: () => {
        console.log('🏠 Navigating to Home');
        navigate('/');
        setIsMobileMenuOpen(false);
      }
    },
    {
      id: 'touch-booking',
      label: 'Touch Booking',
      icon: Smartphone,
      path: '/touch-booking',
      action: () => {
        console.log('📱 Navigating to Touch Booking');
        navigate('/touch-booking');
        setIsMobileMenuOpen(false);
      }
    },
    {
      id: 'bookings',
      label: 'My Bookings',
      icon: Bed,
      path: '/bookings',
      action: () => {
        console.log('🛏️ Navigating to My Bookings');
        navigate('/bookings');
        setIsMobileMenuOpen(false);
      }
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      path: '/profile',
      action: () => {
        console.log('👤 Navigating to Profile');
        navigate('/profile');
        setIsMobileMenuOpen(false);
      }
    }
  ];

  // Admin navigation items (shown only for admin users)
  const adminNavigationItems = [
    {
      id: 'admin-dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      path: '/admin/dashboard',
      action: () => {
        console.log('📊 Navigating to Admin Dashboard');
        navigate('/admin/dashboard');
        setIsMobileMenuOpen(false);
      }
    },
    {
      id: 'admin-hotels',
      label: 'Hotels',
      icon: Building,
      path: '/admin/hotels',
      action: () => {
        console.log('🏨 Navigating to Admin Hotels');
        navigate('/admin/hotels');
        setIsMobileMenuOpen(false);
      }
    }
  ];

  // Combine navigation items based on user role
  const allNavigationItems = admin
    ? [...navigationItems, ...adminNavigationItems]
    : navigationItems;

  // Action buttons with proper navigation
  const actionButtons = [
    {
      id: 'contact',
      label: 'Contact',
      icon: Phone,
      type: 'secondary' as const,
      action: () => {
        console.log('📞 Navigating to Contact');
        // Navigate to homepage and scroll to contact section
        if (location.pathname === '/') {
          const contactElement = document.getElementById('contact');
          if (contactElement) {
            contactElement.scrollIntoView({ behavior: 'smooth' });
          }
        } else {
          navigate('/');
          setTimeout(() => {
            const contactElement = document.getElementById('contact');
            if (contactElement) {
              contactElement.scrollIntoView({ behavior: 'smooth' });
            }
          }, 100);
        }
        setIsMobileMenuOpen(false);
      }
    },
    {
      id: 'touch-booking',
      label: 'Quick Book',
      icon: Smartphone,
      type: 'primary' as const,
      action: () => {
        console.log('📱 Quick Book - Navigating to Touch Booking');
        navigate('/touch-booking');
        setIsMobileMenuOpen(false);
      }
    }
  ];

  return (
    <>
      <nav
        className={cn(
          'fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-in-out',
          isScrolled 
            ? 'py-3 bg-white/95 backdrop-blur-xl border-b border-gray-200/30 shadow-lg shadow-black/5'
            : 'py-5 bg-white/80 backdrop-blur-md border-b border-white/20',
          className
        )}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center">
                <Hotel className="w-5 h-5 text-white" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  One Touch Hotels
                </h1>
                <p className="text-xs text-muted-foreground -mt-1">
                  Book with ease
                </p>
              </div>
            </div>

            {/* Enhanced Desktop Navigation with Scrolling */}
            <div className="hidden md:flex items-center flex-1 max-w-2xl mx-8">
              {isNavScrollable && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={scrollNavLeft}
                  className="mr-2 p-1 h-8 w-8 rounded-full hover:bg-gray-100 transition-all duration-200"
                  aria-label="Scroll navigation left"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
              )}
              
              <div 
                ref={navScrollRef}
                className="flex items-center gap-2 overflow-x-auto scrollbar-hide scroll-smooth"
                style={{ 
                  scrollbarWidth: 'none', 
                  msOverflowStyle: 'none'
                }}
                onScroll={(e) => {
                  const target = e.target as HTMLDivElement;
                  setNavScrollPosition(target.scrollLeft);
                }}
              >
                {allNavigationItems.map((item) => {
                  const isActive = isRouteActive(item.path);
                  return (
                    <Button
                      key={item.id}
                      variant="ghost"
                      className={cn(
                        "relative text-sm font-medium transition-all duration-300 group px-3 py-2 rounded-lg flex items-center gap-2 whitespace-nowrap touch-manipulation",
                        isActive
                          ? "text-primary bg-primary/10 shadow-sm"
                          : "text-gray-700 hover:text-primary hover:bg-primary/5"
                      )}
                      onClick={item.action}
                    >
                      <item.icon className={cn(
                        "w-4 h-4 transition-colors",
                        isActive ? "text-primary" : ""
                      )} />
                      <span className="relative z-10">{item.label}</span>
                      {isActive && (
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/15 to-primary/10 rounded-lg"></div>
                      )}
                      {!isActive && (
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300"></div>
                      )}
                    </Button>
                  );
                })}
              </div>
              
              {isNavScrollable && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={scrollNavRight}
                  className="ml-2 p-1 h-8 w-8 rounded-full hover:bg-gray-100 transition-all duration-200"
                  aria-label="Scroll navigation right"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              )}
            </div>

            {/* Right side actions */}
            <div className="flex items-center gap-3">
              {/* Quick Search Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Focus on search if on home page, otherwise go to home
                  if (location.pathname === '/') {
                    const searchElement = document.getElementById('search');
                    if (searchElement) {
                      searchElement.scrollIntoView({ behavior: 'smooth' });
                      const searchInput = searchElement.querySelector('input');
                      if (searchInput) {
                        setTimeout(() => searchInput.focus(), 500);
                      }
                    }
                  } else {
                    navigate('/');
                    setTimeout(() => {
                      const searchElement = document.getElementById('search');
                      if (searchElement) {
                        searchElement.scrollIntoView({ behavior: 'smooth' });
                      }
                    }, 100);
                  }
                }}
                className="hidden md:flex items-center gap-2 text-gray-600 hover:text-primary transition-colors"
                aria-label="Search hotels"
              >
                <Search className="w-4 h-4" />
                <span className="text-sm">Search</span>
              </Button>

              <StaticNotificationCenter />
              
              {firebaseUser ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <User className="w-4 h-4 text-primary" />
                      </div>
                      {admin && (
                        <Badge className="absolute -top-1 -right-1 w-5 h-5 p-0 bg-green-500 text-white text-xs flex items-center justify-center">
                          A
                        </Badge>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {firebaseUser.displayName || 'User'}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {firebaseUser.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => navigate('/profile')}>
                      <User className="mr-2 h-4 w-4" />
                      Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/bookings')}>
                      <Bed className="mr-2 h-4 w-4" />
                      My Bookings
                    </DropdownMenuItem>
                    {admin && (
                      <DropdownMenuItem onClick={() => navigate('/admin')}>
                        <Shield className="mr-2 h-4 w-4" />
                        Admin Dashboard
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <div className="hidden md:flex items-center gap-2">
                  <Button variant="ghost" onClick={() => navigateToAuth('signin')}>
                    Sign In
                  </Button>
                  <Button variant="outline" onClick={() => navigateToAuth('signup')}>
                    Sign Up
                  </Button>
                  <Button onClick={() => navigateToAuth('admin')} className="btn-primary">
                    Admin
                  </Button>
                </div>
              )}

              {/* Enhanced Mobile Menu Button */}
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden flex items-center justify-center w-12 h-12 rounded-lg bg-white/80 backdrop-blur-sm border border-gray-200/50 hover:bg-white hover:shadow-lg transition-all duration-300 touch-manipulation active:scale-95"
                onClick={toggleMobileMenu}
                onTouchStart={handleTouchStart}
                aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
                aria-expanded={isMobileMenuOpen}
              >
                <div className="relative w-6 h-6 flex flex-col justify-center items-center">
                  <span className={cn(
                    "block w-6 h-0.5 bg-gray-700 transition-all duration-300 transform origin-center",
                    isMobileMenuOpen ? "rotate-45 translate-y-0.5" : "rotate-0 -translate-y-1"
                  )} />
                  <span className={cn(
                    "block w-6 h-0.5 bg-gray-700 transition-all duration-300",
                    isMobileMenuOpen ? "opacity-0 scale-0" : "opacity-100 scale-100"
                  )} />
                  <span className={cn(
                    "block w-6 h-0.5 bg-gray-700 transition-all duration-300 transform origin-center",
                    isMobileMenuOpen ? "-rotate-45 -translate-y-0.5" : "rotate-0 translate-y-1"
                  )} />
                </div>
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Enhanced Mobile Menu Overlay */}
      <div
        className={cn(
          "fixed inset-0 bg-white/98 backdrop-blur-xl z-40 flex flex-col transition-all duration-500 ease-in-out transform md:hidden border-l border-gray-200/50",
          isMobileMenuOpen ? "translate-x-0 opacity-100" : "translate-x-full opacity-0"
        )}
        ref={mobileMenuRef}
        onTouchStart={handleTouchStart}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-blue-50/30"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.03),transparent_50%)]"></div>

        {/* Mobile Menu Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200/50">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <Hotel className="w-4 h-4 text-white" />
            </div>
            <span className="text-lg font-semibold text-gray-900">Menu</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMobileMenu}
            className="p-2 rounded-lg hover:bg-gray-100 touch-manipulation"
            aria-label="Close menu"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Scrollable Navigation Content */}
        <div className="flex-1 overflow-y-auto overscroll-contain">
          <nav className="relative z-10 flex flex-col space-y-3 text-lg p-6">
            {/* Enhanced Navigation Items */}
            {allNavigationItems.map((item) => {
              const isActive = isRouteActive(item.path);
              return (
                <Button
                  key={item.id}
                  variant="ghost"
                  className={cn(
                    "group text-left p-4 rounded-xl backdrop-blur-sm border transition-all duration-300 hover:scale-[1.02] hover:shadow-lg shadow-sm justify-start touch-manipulation active:scale-95",
                    isActive
                      ? "bg-primary/10 border-primary/30 text-primary shadow-md"
                      : "bg-white/80 border-gray-200/50 hover:bg-white hover:border-primary/30 hover:text-primary"
                  )}
                  onClick={item.action}
                >
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "w-10 h-10 rounded-lg flex items-center justify-center transition-colors",
                      isActive
                        ? "bg-primary/20"
                        : "bg-primary/10 group-hover:bg-primary/20"
                    )}>
                      <item.icon className="w-5 h-5 text-primary" />
                    </div>
                    <span className={cn(
                      "font-medium transition-colors",
                      isActive
                        ? "text-primary"
                        : "text-gray-700 group-hover:text-primary"
                    )}>{item.label}</span>
                    {isActive && (
                      <Badge className="ml-auto bg-primary/20 text-primary border-primary/30">
                        Active
                      </Badge>
                    )}
                  </div>
                </Button>
              );
            })}

            {/* Enhanced Action Buttons */}
            {actionButtons.map((button) => (
              <Button
                key={button.id}
                className={cn(
                  "group text-left p-4 rounded-xl border transition-all duration-300 hover:scale-[1.02] hover:shadow-lg shadow-sm justify-start touch-manipulation active:scale-95",
                  button.type === 'primary' && "bg-gradient-to-r from-primary to-primary/80 text-white border-primary/20 hover:from-primary/90 hover:to-primary/70",
                  button.type === 'secondary' && "bg-gradient-to-r from-purple-500 to-pink-500 text-white border-purple-400/20 hover:from-purple-400 hover:to-pink-400"
                )}
                onClick={button.action}
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                    <button.icon className="w-5 h-5 text-white" />
                  </div>
                  <span className="font-medium text-white">{button.label}</span>
                </div>
              </Button>
            ))}

            {/* Auth buttons for mobile */}
            {!firebaseUser && (
              <div className="space-y-3 pt-4 border-t border-gray-200/50">
                <Button
                  onClick={() => navigateToAuth('signin')}
                  className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white touch-manipulation active:scale-95"
                >
                  <User className="w-5 h-5 mr-2" />
                  Sign In
                </Button>
                <Button
                  onClick={() => navigateToAuth('signup')}
                  className="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white touch-manipulation active:scale-95"
                >
                  <User className="w-5 h-5 mr-2" />
                  Sign Up
                </Button>
                <Button
                  onClick={() => navigateToAuth('admin')}
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white touch-manipulation active:scale-95"
                >
                  <Shield className="w-5 h-5 mr-2" />
                  Admin Login
                </Button>
              </div>
            )}

            {/* User info for mobile */}
            {firebaseUser && (
              <div className="pt-4 border-t border-gray-200/50">
                <div className="p-4 bg-white/80 rounded-xl border border-gray-200/50">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{firebaseUser.displayName || 'User'}</p>
                      <p className="text-sm text-gray-500">{firebaseUser.email}</p>
                    </div>
                    {admin && (
                      <Badge className="bg-green-500 text-white">Admin</Badge>
                    )}
                  </div>
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    className="w-full touch-manipulation active:scale-95"
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign Out
                  </Button>
                </div>
              </div>
            )}
          </nav>
        </div>
      </div>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <Button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 z-50 w-12 h-12 rounded-full bg-primary hover:bg-primary/90 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 touch-manipulation active:scale-95"
          aria-label="Scroll to top"
        >
          <ArrowUp className="w-5 h-5" />
        </Button>
      )}

    </>
  );
};

export default Navbar;
