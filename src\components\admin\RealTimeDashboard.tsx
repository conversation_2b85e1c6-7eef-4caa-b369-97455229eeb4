// Real-time admin dashboard component
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { RealtimeService } from '@/services/realtimeService';
import { getAllHotels } from '@/services/hotelService';
import { Hotel, Booking } from '@/types/hotel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import RealTimeAvailability from '@/components/RealTimeAvailability';
import { 
  Calendar, 
  Users, 
  IndianRupee, 
  TrendingUp, 
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Hotel as HotelIcon,
  Activity
} from 'lucide-react';
import { toast } from 'sonner';

interface RealTimeDashboardProps {
  className?: string;
}

const RealTimeDashboard: React.FC<RealTimeDashboardProps> = ({ className }) => {
  const { admin } = useAuth();
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [realtimeBookings, setRealtimeBookings] = useState<Booking[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    loadHotels();
    subscribeToBookings();

    return () => {
      const realtimeService = RealtimeService.getInstance();
      realtimeService.unsubscribe('all_bookings');
    };
  }, []);

  const loadHotels = async () => {
    try {
      const hotelsData = await getAllHotels();
      setHotels(hotelsData);
      if (hotelsData.length > 0) {
        setSelectedHotel(hotelsData[0].id);
      }
    } catch (error) {
      console.error('Error loading hotels:', error);
      toast.error('Failed to load hotels');
    } finally {
      setLoading(false);
    }
  };

  const subscribeToBookings = () => {
    const realtimeService = RealtimeService.getInstance();
    
    realtimeService.subscribeToAllBookings((bookings) => {
      setRealtimeBookings(bookings);
      setLastUpdate(new Date());
    });
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      case 'completed':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  // Calculate real-time statistics
  const todayBookings = realtimeBookings.filter(booking => {
    const bookingDate = new Date(booking.createdAt);
    const today = new Date();
    return bookingDate.toDateString() === today.toDateString();
  });

  const pendingBookings = realtimeBookings.filter(b => b.status === 'pending');
  const confirmedBookings = realtimeBookings.filter(b => b.status === 'confirmed');
  const totalRevenue = confirmedBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Real-time Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Live updates • Last updated {formatTimeAgo(lastUpdate)}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-green-500 animate-pulse" />
          <span className="text-sm text-green-600 font-medium">Live</span>
        </div>
      </div>

      {/* Real-time Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Bookings</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{todayBookings.length}</div>
            <p className="text-xs text-muted-foreground">
              {pendingBookings.length} pending approval
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Bookings</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{confirmedBookings.length}</div>
            <p className="text-xs text-muted-foreground">
              Confirmed reservations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue Today</CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              From confirmed bookings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hotels Active</CardTitle>
            <HotelIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{hotels.length}</div>
            <p className="text-xs text-muted-foreground">
              Properties online
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Real-time Availability */}
      <div className="grid lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Hotel Availability</CardTitle>
            <div className="flex gap-2">
              {hotels.map((hotel) => (
                <Button
                  key={hotel.id}
                  variant={selectedHotel === hotel.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedHotel(hotel.id)}
                >
                  {hotel.name}
                </Button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {selectedHotel && (
              <RealTimeAvailability 
                hotelId={selectedHotel} 
                showHeader={false}
              />
            )}
          </CardContent>
        </Card>

        {/* Recent Bookings */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Bookings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {realtimeBookings.slice(0, 10).map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(booking.status)}
                    <div>
                      <p className="font-medium text-sm">{booking.guestInfo.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(booking.checkInDate).toLocaleDateString()} - {new Date(booking.checkOutDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={getStatusBadgeVariant(booking.status)}>
                      {booking.status}
                    </Badge>
                    <p className="text-xs text-muted-foreground mt-1">
                      ₹{booking.totalAmount.toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
              
              {realtimeBookings.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No bookings yet</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Pending Actions */}
      {pendingBookings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-yellow-500" />
              Pending Actions ({pendingBookings.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {pendingBookings.slice(0, 5).map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div>
                    <p className="font-medium">{booking.guestInfo.name}</p>
                    <p className="text-sm text-muted-foreground">
                      Booking for {new Date(booking.checkInDate).toLocaleDateString()} • ₹{booking.totalAmount.toLocaleString()}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      View Details
                    </Button>
                    <Button size="sm">
                      Approve
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default RealTimeDashboard;
