// Enhanced Forgot Password Page with Email Reset Link Functionality
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Mail,
  ArrowLeft,
  Shield,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  Building,
  X,
  RefreshCw,
  Send,
  Key,
  Lock,
  Eye,
  EyeOff,
  Star,
  Heart,
  Users,
  Award
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { sendPasswordResetEmail } from 'firebase/auth';
import { auth } from '@/lib/firebase';

const ForgotPasswordPage: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState('');
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const formRef = useRef<HTMLDivElement>(null);
  const emailInputRef = useRef<HTMLInputElement>(null);

  // Auto-focus email input on mount
  useEffect(() => {
    emailInputRef.current?.focus();
  }, []);

  // Scroll tracking
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? (scrollTop / docHeight) * 100 : 0;
      setScrollProgress(scrollPercent);

      if (scrollTop > 50) {
        setIsVisible(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Countdown timer for resend functionality
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // Handle escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => document.removeEventListener('keydown', handleEscapeKey);
  }, []);

  // Form validation
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Handle close/back navigation
  const handleClose = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/signin');
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError('Email address is required');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const actionCodeSettings = {
        url: `${window.location.origin}/reset-password`,
        handleCodeInApp: false,
      };

      await sendPasswordResetEmail(auth, email, actionCodeSettings);
      setEmailSent(true);
      setCountdown(60); // 60 second cooldown
      toast.success('Password reset email sent successfully!');
    } catch (error: any) {
      console.error('Password reset error:', error);

      let errorMessage = 'Failed to send reset email. Please try again';

      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email address';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address format';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many requests. Please try again later';
          break;
        case 'auth/network-request-failed':
          errorMessage = 'Network error. Please check your connection';
          break;
        default:
          errorMessage = error.message || 'Failed to send reset email. Please try again';
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle resend email
  const handleResend = async () => {
    if (countdown > 0) return;
    
    setLoading(true);
    setError('');

    try {
      const actionCodeSettings = {
        url: `${window.location.origin}/reset-password`,
        handleCodeInApp: false,
      };

      await sendPasswordResetEmail(auth, email, actionCodeSettings);
      setCountdown(60);
      toast.success('Password reset email sent again!');
    } catch (error: any) {
      console.error('Resend password reset error:', error);

      let errorMessage = 'Failed to resend email. Please try again';

      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email address';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address format';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many requests. Please try again later';
          break;
        default:
          errorMessage = error.message || 'Failed to resend email. Please try again';
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Scroll to form
  const scrollToForm = () => {
    formRef.current?.scrollIntoView({ 
      behavior: 'smooth', 
      block: 'start' 
    });
  };

  // Security tips data
  const securityTips = [
    {
      icon: Shield,
      title: 'Secure Process',
      description: 'Password reset links are encrypted and expire in 1 hour'
    },
    {
      icon: Clock,
      title: 'Quick Recovery',
      description: 'Reset your password in under 2 minutes'
    },
    {
      icon: CheckCircle,
      title: 'Account Safety',
      description: 'Your account remains secure during the reset process'
    },
    {
      icon: Key,
      title: 'Strong Passwords',
      description: 'Create a strong password with 8+ characters'
    }
  ];

  // Stats data
  const stats = [
    { icon: Users, value: '10K+', label: 'Users Protected' },
    { icon: Shield, value: '99.9%', label: 'Security Rate' },
    { icon: Clock, value: '<2min', label: 'Reset Time' },
    { icon: Award, value: '24/7', label: 'Support' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative pt-20">
      {/* Scroll Progress Bar */}
      <div className="fixed top-16 left-0 w-full h-1 bg-gray-200 z-50">
        <div 
          className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300 ease-out"
          style={{ width: `${scrollProgress}%` }}
        />
      </div>

      {/* Close Button - Fixed Position */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClose}
        className="fixed top-20 right-4 z-50 rounded-full w-10 h-10 p-0 hover:bg-red-50 hover:text-red-600 transition-all duration-200 group bg-white/90 backdrop-blur-sm shadow-lg"
        title="Close (ESC)"
      >
        <X className="w-5 h-5 text-gray-500 group-hover:text-red-600 transition-colors" />
      </Button>

      {/* Floating Action Button */}
      {isVisible && (
        <Button
          onClick={scrollToForm}
          className="fixed right-4 bottom-20 z-40 rounded-full w-12 h-12 shadow-lg hover:shadow-xl transition-all duration-300"
          title="Go to Form"
        >
          <Key className="w-5 h-5" />
        </Button>
      )}

      <div className="container mx-auto px-4 py-8 lg:py-12">
        {/* Mobile Welcome Banner */}
        <div className="lg:hidden mb-8 text-center pt-4">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            Reset Your Password
          </h1>
          <p className="text-gray-600 text-sm">
            We'll send you a secure reset link
          </p>
          <Button 
            onClick={scrollToForm}
            className="mt-4 w-full sm:w-auto"
            size="lg"
          >
            Reset Password
            <Key className="w-4 h-4 ml-2" />
          </Button>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 max-w-7xl mx-auto">
          {/* Left Side - Information & Security Tips */}
          <div className="space-y-8 lg:sticky lg:top-24 lg:h-fit">
            {/* Hero Section */}
            <div className="text-center lg:text-left">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Forgot Your
                <span className="text-primary block sm:inline sm:ml-2">
                  Password?
                </span>
              </h1>
              <p className="text-lg text-gray-600 mb-6">
                Don't worry! It happens to everyone. Enter your email address and 
                we'll send you a secure link to reset your password.
              </p>
              
              {/* Stats */}
              <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-2 xl:grid-cols-4 gap-4 mb-8">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center p-4 bg-white/60 rounded-xl border border-gray-200">
                    <stat.icon className="w-6 h-6 text-primary mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                    <div className="text-xs text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Security Tips */}
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-900 text-center lg:text-left">
                How It Works
              </h2>
              <div className="grid sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4">
                {securityTips.map((tip, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 bg-white/60 rounded-xl border border-gray-200 hover:bg-white/80 hover:scale-105 transition-all duration-300 hover:shadow-lg">
                    <div className="p-2 bg-primary/10 rounded-lg flex-shrink-0">
                      <tip.icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">{tip.title}</h3>
                      <p className="text-sm text-gray-600">{tip.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="bg-gradient-to-r from-primary/5 to-blue-100/50 rounded-2xl p-6 text-center lg:text-left">
              <div className="flex items-center justify-center lg:justify-start gap-2 mb-3">
                <Shield className="w-5 h-5 text-primary" />
                <span className="font-semibold text-gray-900">Secure & Private</span>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Your password reset is protected with enterprise-grade security. 
                Reset links expire automatically for your safety.
              </p>
              <div className="flex items-center justify-center lg:justify-start gap-4">
                <Badge variant="secondary" className="text-xs">256-bit SSL</Badge>
                <Badge variant="secondary" className="text-xs">Auto-Expire</Badge>
                <Badge variant="secondary" className="text-xs">Encrypted</Badge>
              </div>
            </div>
          </div>

          {/* Right Side - Reset Form */}
          <div ref={formRef} className="w-full">
            <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 relative">
              {/* Card Close Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="absolute top-4 right-4 rounded-full w-8 h-8 p-0 hover:bg-red-50 transition-all duration-200 z-10 group"
                title="Close (ESC)"
              >
                <X className="w-4 h-4 text-gray-400 group-hover:text-red-500 transition-colors" />
              </Button>

              <CardHeader className="text-center pb-6 pr-12">
                <div className="mx-auto mb-4 p-4 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full w-fit">
                  <Key className="w-8 h-8 text-primary" />
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900">
                  {emailSent ? 'Check Your Email' : 'Reset Password'}
                </CardTitle>
                <p className="text-gray-600 mt-2">
                  {emailSent 
                    ? `We've sent a password reset link to ${email}`
                    : 'Enter your email address to receive a reset link'
                  }
                </p>
              </CardHeader>

              <CardContent className="space-y-6">
                {!emailSent ? (
                  /* Reset Form */
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <div className="relative">
                        <Input
                          ref={emailInputRef}
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => {
                            setEmail(e.target.value);
                            setError('');
                          }}
                          placeholder="Enter your email address"
                          className={cn("pl-10", error && "border-red-500")}
                          disabled={loading}
                        />
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      </div>
                      {error && <p className="text-sm text-red-500 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {error}
                      </p>}
                    </div>

                    <Button 
                      type="submit" 
                      disabled={loading || !email.trim()}
                      className="w-full h-12 text-base font-semibold"
                      size="lg"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Sending Reset Link...
                        </>
                      ) : (
                        <>
                          Send Reset Link
                          <Send className="w-4 h-4 ml-2" />
                        </>
                      )}
                    </Button>
                  </form>
                ) : (
                  /* Success State */
                  <div className="text-center space-y-6">
                    <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold text-gray-900">Email Sent Successfully!</h3>
                      <p className="text-sm text-gray-600">
                        Check your inbox and click the reset link to create a new password.
                      </p>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                        <div className="text-sm text-blue-800">
                          <p className="font-medium mb-1">Didn't receive the email?</p>
                          <ul className="text-xs space-y-1">
                            <li>• Check your spam/junk folder</li>
                            <li>• Make sure you entered the correct email</li>
                            <li>• The link expires in 1 hour</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <Button
                      onClick={handleResend}
                      disabled={loading || countdown > 0}
                      variant="outline"
                      className="w-full"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Sending...
                        </>
                      ) : countdown > 0 ? (
                        <>
                          <Clock className="w-4 h-4 mr-2" />
                          Resend in {countdown}s
                        </>
                      ) : (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2" />
                          Resend Email
                        </>
                      )}
                    </Button>
                  </div>
                )}

                {/* Navigation Links */}
                <Separator />
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4 text-center">
                  <Link to="/signin" className="flex items-center gap-2 text-sm text-primary hover:underline">
                    <ArrowLeft className="w-4 h-4" />
                    Back to Sign In
                  </Link>
                  <Link to="/signup" className="text-sm text-gray-600 hover:text-primary">
                    Don't have an account? <span className="font-medium">Sign up</span>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-200 py-8 mt-12">
        <div className="container mx-auto px-4 text-center">
          <p className="text-sm text-gray-600">
            © 2024 One Touch Hotels. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
