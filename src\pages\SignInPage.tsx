// Enhanced Sign In Page with Scroll Options and Full Responsiveness
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { signInUser } from '@/services/authService';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Mail,
  Lock,
  Eye,
  EyeOff,
  LogIn,
  Shield,
  Star,
  Heart,
  Clock,
  Loader2,
  Building,
  Users,
  Award,
  CheckCircle,
  Gift,
  Zap,
  X
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const SignInPage: React.FC = () => {
  const navigate = useNavigate();
  const { refreshUserData, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const formRef = useRef<HTMLDivElement>(null);
  const featuresRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/profile');
    }
  }, [isAuthenticated, navigate]);

  // Scroll tracking and animations
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      setScrollProgress(scrollPercent);

      // Trigger visibility animations
      if (scrollTop > 100) {
        setIsVisible(true);
      }
    };

    const handleScrollAnimations = () => {
      // Intersection Observer for smooth animations
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.classList.add('animate-in', 'slide-in-from-left', 'duration-700');
            }
          });
        },
        { threshold: 0.1 }
      );

      // Observe feature cards
      if (featuresRef.current) {
        const featureCards = featuresRef.current.querySelectorAll('.feature-card');
        featureCards.forEach((card) => observer.observe(card));
      }

      return () => observer.disconnect();
    };

    window.addEventListener('scroll', handleScroll);
    const cleanup = handleScrollAnimations();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      cleanup();
    };
  }, []);

  // Smooth scroll functions
  const scrollToForm = () => {
    formRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };

  const scrollToFeatures = () => {
    featuresRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };

  // Check if form has data
  const hasFormData = () => {
    return formData.email || formData.password;
  };

  // Handle close/back navigation
  const handleClose = () => {
    // If form has data, show confirmation
    if (hasFormData()) {
      const confirmed = window.confirm(
        'Are you sure you want to leave? Your entered data will be lost.'
      );
      if (!confirmed) return;
    }

    // Check if user came from another page in the app
    if (window.history.length > 1) {
      navigate(-1); // Go back to previous page
    } else {
      navigate('/'); // Go to home page
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => document.removeEventListener('keydown', handleEscapeKey);
  }, []);

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);

    try {
      await signInUser(formData.email, formData.password);
      await refreshUserData();

      toast.success(`Welcome back!`);
      navigate('/profile');
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign in. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  // Demo credentials
  const demoCredentials = [
    { email: '<EMAIL>', password: 'demo123', type: 'Guest User' },
    { email: '<EMAIL>', password: 'premium123', type: 'Premium Member' }
  ];

  const fillDemoCredentials = (email: string, password: string) => {
    setFormData({ email, password });
    toast.info('Demo credentials filled. Click Sign In to continue.');
  };

  // Features data
  const features = [
    {
      icon: Star,
      title: 'Exclusive Deals',
      description: 'Access member-only discounts and special offers'
    },
    {
      icon: Shield,
      title: 'Secure Booking',
      description: 'Your bookings are protected with advanced security'
    },
    {
      icon: Heart,
      title: 'Personalized Service',
      description: 'Tailored recommendations based on your preferences'
    },
    {
      icon: Clock,
      title: 'Instant Confirmation',
      description: 'Get immediate booking confirmations and updates'
    }
  ];

  const stats = [
    { icon: Building, value: '50+', label: 'Premium Hotels' },
    { icon: Users, value: '10K+', label: 'Happy Guests' },
    { icon: Award, value: '4.9', label: 'Average Rating' },
    { icon: Star, value: '99%', label: 'Satisfaction Rate' }
  ];

  const quickActions = [
    {
      icon: Gift,
      title: 'Special Offers',
      description: 'View current promotions',
      color: 'bg-purple-100 text-purple-700'
    },
    {
      icon: Zap,
      title: 'Quick Booking',
      description: 'Book in under 2 minutes',
      color: 'bg-blue-100 text-blue-700'
    },
    {
      icon: CheckCircle,
      title: 'Manage Bookings',
      description: 'View and modify reservations',
      color: 'bg-green-100 text-green-700'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative pt-20">
      {/* Scroll Progress Bar */}
      <div className="fixed top-16 left-0 w-full h-1 bg-gray-200 z-50">
        <div
          className="h-full bg-gradient-to-r from-primary to-blue-600 transition-all duration-300 ease-out"
          style={{ width: `${scrollProgress}%` }}
        />
      </div>

      {/* Floating Action Buttons */}
      {isVisible && (
        <div className="fixed right-4 bottom-20 z-40 flex flex-col gap-2">
          <Button
            onClick={scrollToForm}
            size="sm"
            className="rounded-full w-12 h-12 shadow-lg hover:shadow-xl transition-all duration-300"
            title="Go to Sign In Form"
          >
            <LogIn className="w-4 h-4" />
          </Button>
          <Button
            onClick={scrollToFeatures}
            variant="outline"
            size="sm"
            className="rounded-full w-12 h-12 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 backdrop-blur-sm"
            title="View Features"
          >
            <Star className="w-4 h-4" />
          </Button>
          <Button
            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            variant="outline"
            size="sm"
            className="rounded-full w-12 h-12 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 backdrop-blur-sm"
            title="Back to Top"
          >
            ↑
          </Button>
        </div>
      )}

      {/* Close Button - Fixed Position */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClose}
        className="fixed top-20 right-4 z-50 rounded-full w-10 h-10 p-0 hover:bg-red-50 hover:text-red-600 transition-all duration-200 group bg-white/90 backdrop-blur-sm shadow-lg"
        title="Close Sign In (ESC)"
      >
        <X className="w-5 h-5 text-gray-500 group-hover:text-red-600 transition-colors" />
      </Button>

      <div className="container mx-auto px-4 py-8 lg:py-12">
        {/* Mobile Welcome Banner */}
        <div className="lg:hidden mb-8 text-center pt-4">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-600 text-sm">
            Sign in to access your account
          </p>
          <Button
            onClick={scrollToForm}
            className="mt-4 w-full sm:w-auto"
            size="lg"
          >
            Sign In Now
            <LogIn className="w-4 h-4 ml-2" />
          </Button>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 max-w-7xl mx-auto">
          {/* Left Side - Welcome & Features */}
          <div className="space-y-8 lg:sticky lg:top-24 lg:h-fit">
            {/* Hero Section */}
            <div className="text-center lg:text-left">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Welcome Back to
                <span className="text-primary block sm:inline sm:ml-2">
                  One Touch Hotels
                </span>
              </h1>
              <p className="text-lg text-gray-600 mb-6">
                Sign in to access your account, manage bookings, and enjoy 
                personalized experiences across our premium hotel network.
              </p>
              
              {/* Stats */}
              <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-2 xl:grid-cols-4 gap-4 mb-8">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center p-4 bg-white/60 rounded-xl border border-gray-200">
                    <stat.icon className="w-6 h-6 text-primary mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                    <div className="text-xs text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Features */}
            <div ref={featuresRef} className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-900 text-center lg:text-left">
                What You Get
              </h2>
              <div className="grid sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4">
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className="feature-card flex items-start gap-3 p-4 bg-white/60 rounded-xl border border-gray-200 hover:bg-white/80 hover:scale-105 transition-all duration-300 hover:shadow-lg"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="p-2 bg-primary/10 rounded-lg flex-shrink-0 hover:bg-primary/20 transition-colors">
                      <feature.icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">{feature.title}</h3>
                      <p className="text-sm text-gray-600">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-900 text-center lg:text-left">
                Quick Actions
              </h2>
              <div className="grid sm:grid-cols-3 lg:grid-cols-1 xl:grid-cols-3 gap-4">
                {quickActions.map((action, index) => (
                  <div key={index} className="text-center p-4 bg-white/60 rounded-xl border border-gray-200 hover:bg-white/80 transition-colors cursor-pointer">
                    <div className={cn("w-12 h-12 rounded-lg mx-auto mb-3 flex items-center justify-center", action.color)}>
                      <action.icon className="w-6 h-6" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-1">{action.title}</h3>
                    <p className="text-xs text-gray-600">{action.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="bg-gradient-to-r from-primary/5 to-purple-100/50 rounded-2xl p-6 text-center lg:text-left">
              <div className="flex items-center justify-center lg:justify-start gap-2 mb-3">
                <Shield className="w-5 h-5 text-primary" />
                <span className="font-semibold text-gray-900">Secure & Trusted</span>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Your account is protected with enterprise-grade security and encryption.
              </p>
              <div className="flex items-center justify-center lg:justify-start gap-4">
                <Badge variant="secondary" className="text-xs">SSL Encrypted</Badge>
                <Badge variant="secondary" className="text-xs">2FA Available</Badge>
                <Badge variant="secondary" className="text-xs">GDPR Compliant</Badge>
              </div>
            </div>
          </div>

          {/* Right Side - Sign In Form */}
          <div ref={formRef} className="w-full">
            <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 relative">
              {/* Card Close Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="absolute top-4 right-4 rounded-full w-8 h-8 p-0 hover:bg-red-50 transition-all duration-200 z-10 group"
                title="Close Sign In (ESC)"
              >
                <X className="w-4 h-4 text-gray-400 group-hover:text-red-500 transition-colors" />
              </Button>

              <CardHeader className="text-center pb-6 pr-12">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  Sign In to Your Account
                </CardTitle>
                <p className="text-gray-600 mt-2">
                  Enter your credentials to access your dashboard
                </p>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <div className="relative">
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="Enter your email address"
                        className={cn("pl-10", errors.email && "border-red-500")}
                        autoComplete="email"
                      />
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    </div>
                    {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password">Password</Label>
                      <Link
                        to="/forgot-password"
                        className="text-sm text-primary hover:underline font-medium transition-colors"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={(e) => handleInputChange('password', e.target.value)}
                        placeholder="Enter your password"
                        className={cn("pl-10 pr-10", errors.password && "border-red-500")}
                        autoComplete="current-password"
                      />
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </Button>
                    </div>
                    {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
                  </div>

                  {/* Remember Me */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="rememberMe"
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <label htmlFor="rememberMe" className="text-sm text-gray-600">
                        Remember me
                      </label>
                    </div>
                  </div>

                  <Button 
                    type="submit" 
                    disabled={loading}
                    className="w-full h-12 text-base font-semibold"
                    size="lg"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Signing In...
                      </>
                    ) : (
                      <>
                        Sign In
                        <LogIn className="w-4 h-4 ml-2" />
                      </>
                    )}
                  </Button>
                </form>

                {/* Demo Credentials */}
                <div className="space-y-4">
                  <Separator />
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-3">Try with demo credentials:</p>
                    <div className="grid gap-2">
                      {demoCredentials.map((demo, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => fillDemoCredentials(demo.email, demo.password)}
                          className="w-full justify-between text-xs"
                        >
                          <span>{demo.type}</span>
                          <span className="text-gray-500">{demo.email}</span>
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Sign Up Link */}
                <Separator />
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Don't have an account?{' '}
                    <Link to="/signup" className="text-primary hover:underline font-medium">
                      Sign up for free
                    </Link>
                  </p>
                </div>

                {/* Admin Login Link */}
                <div className="text-center pt-4 border-t border-gray-200">
                  <p className="text-xs text-gray-500 mb-2">Hotel Staff?</p>
                  <Link to="/admin/login">
                    <Button variant="ghost" size="sm" className="text-xs">
                      <Shield className="w-3 h-3 mr-1" />
                      Admin Login
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-200 py-8 mt-12">
        <div className="container mx-auto px-4 text-center">
          <p className="text-sm text-gray-600">
            © 2024 One Touch Hotels. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SignInPage;
