// Simple booking form component
import React, { useState } from 'react';
import { cn, generateBookingReference, calculateNights, formatCurrency } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Calendar,
  Users,
  Phone,
  Mail,
  MapPin,
  CheckCircle,
  Clock,
  Home
} from 'lucide-react';
import { toast } from 'sonner';
import { createBooking } from '@/services/bookingService';
import { CreateBookingRequest, BookingRoom } from '@/types/booking';
import { Timestamp } from 'firebase/firestore';
import { useAuth } from '@/contexts/AuthContext';

interface SimpleBookingProps {
  className?: string;
}

const SimpleBooking: React.FC<SimpleBookingProps> = ({ className }) => {
  const [step, setStep] = useState<'form' | 'confirmation'>('form');
  const [loading, setLoading] = useState(false);
  const { firebaseUser } = useAuth();
  
  const [bookingData, setBookingData] = useState({
    checkIn: '',
    checkOut: '',
    adults: '2',
    children: '0',
    rooms: '1',
    name: '',
    email: '',
    phone: '',
    specialRequests: '',
    roomPreference: '',
    hotelPreference: 'any'
  });

  const [bookingConfirmation, setBookingConfirmation] = useState<{
    bookingId: string;
    bookingReference: string;
    totalAmount: number;
  } | null>(null);

  const handleInputChange = (field: string, value: string) => {
    setBookingData(prev => ({ ...prev, [field]: value }));
  };

  // Helper function to get hotel data based on preference
  const getHotelByPreference = (preference: string) => {
    const hotels = {
      'arunachala-palace': {
        id: 'hotel-1',
        name: 'Arunachala Palace Hotel',
        address: 'Temple Street, Tiruvannamalai'
      },
      'ramana-ashram': {
        id: 'hotel-2',
        name: 'Ramana Ashram Guest House',
        address: 'Ramana Nagar, Tiruvannamalai'
      },
      'business-hotel': {
        id: 'hotel-3',
        name: 'Tiruvannamalai Business Hotel',
        address: 'Gandhi Road, Tiruvannamalai'
      },
      'family-resort': {
        id: 'hotel-4',
        name: 'Family Paradise Resort',
        address: 'Chengam Road, Tiruvannamalai'
      },
      'any': {
        id: 'hotel-1',
        name: 'Arunachala Palace Hotel',
        address: 'Temple Street, Tiruvannamalai'
      }
    };
    return hotels[preference as keyof typeof hotels] || hotels.any;
  };

  // Helper function to get room data based on preference
  const getRoomByPreference = (preference: string, quantity: number) => {
    const rooms = {
      'heritage-suite': {
        id: 'room-1',
        type: 'Heritage Suite',
        pricePerNight: 8500,
        amenities: ['Temple View', 'Heritage Architecture', 'Premium Amenities'],
        maxOccupancy: 3
      },
      'deluxe-room': {
        id: 'room-2',
        type: 'Deluxe Room',
        pricePerNight: 5500,
        amenities: ['AC', 'WiFi', 'Room Service', 'Modern Amenities'],
        maxOccupancy: 2
      },
      'family-suite': {
        id: 'room-3',
        type: 'Family Suite',
        pricePerNight: 7200,
        amenities: ['Spacious', 'Family Friendly', 'Multiple Beds'],
        maxOccupancy: 4
      },
      'standard-room': {
        id: 'room-4',
        type: 'Standard Room',
        pricePerNight: 3500,
        amenities: ['AC', 'WiFi', 'Basic Amenities'],
        maxOccupancy: 2
      },
      '': {
        id: 'room-2',
        type: 'Deluxe Room',
        pricePerNight: 5500,
        amenities: ['AC', 'WiFi', 'Room Service', 'Modern Amenities'],
        maxOccupancy: 2
      }
    };
    return rooms[preference as keyof typeof rooms] || rooms[''];
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!bookingData.checkIn || !bookingData.checkOut || !bookingData.name || !bookingData.email || !bookingData.phone) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Validate dates
    const checkInDate = new Date(bookingData.checkIn);
    const checkOutDate = new Date(bookingData.checkOut);
    
    if (checkInDate >= checkOutDate) {
      toast.error('Check-out date must be after check-in date');
      return;
    }

    if (checkInDate < new Date()) {
      toast.error('Check-in date cannot be in the past');
      return;
    }

    setLoading(true);
    
    try {
      // Calculate booking details
      const nights = calculateNights(checkInDate, checkOutDate);
      const roomQuantity = parseInt(bookingData.rooms);
      const adults = parseInt(bookingData.adults);
      const children = parseInt(bookingData.children);

      // Determine hotel and room details based on preference
      const hotelData = getHotelByPreference(bookingData.hotelPreference);
      const roomData = getRoomByPreference(bookingData.roomPreference, roomQuantity);

      // Calculate pricing
      const subtotal = roomData.pricePerNight * nights * roomQuantity;
      const taxes = Math.round(subtotal * 0.18); // 18% GST
      const fees = 200; // Service fee
      const totalAmount = subtotal + taxes + fees;

      // Create booking request
      const bookingRequest: CreateBookingRequest = {
        hotelId: hotelData.id,
        hotelName: hotelData.name,
        guestName: bookingData.name,
        guestEmail: bookingData.email,
        guestPhone: bookingData.phone,
        checkIn: checkInDate,
        checkOut: checkOutDate,
        rooms: [{
          roomId: roomData.id,
          roomType: roomData.type,
          quantity: roomQuantity,
          pricePerNight: roomData.pricePerNight,
          amenities: roomData.amenities,
          maxOccupancy: roomData.maxOccupancy
        }],
        adults,
        children,
        specialRequests: bookingData.specialRequests ? {
          otherRequests: bookingData.specialRequests
        } : undefined,
        guestNotes: bookingData.specialRequests,
        bookingSource: 'website',
        userId: firebaseUser?.uid // Add user ID if user is authenticated
      };

      // Create booking in Firestore
      const bookingId = await createBooking(bookingRequest);
      const bookingReference = generateBookingReference();

      // Set confirmation data
      setBookingConfirmation({
        bookingId,
        bookingReference,
        totalAmount
      });

      toast.success('Booking request submitted successfully!');
      setStep('confirmation');
    } catch (error) {
      console.error('Booking submission error:', error);
      toast.error('Failed to submit booking request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setStep('form');
    setBookingData({
      checkIn: '',
      checkOut: '',
      guests: '2',
      rooms: '1',
      name: '',
      email: '',
      phone: '',
      specialRequests: '',
      roomPreference: ''
    });
  };

  const calculateNights = () => {
    if (!bookingData.checkIn || !bookingData.checkOut) return 0;
    const checkIn = new Date(bookingData.checkIn);
    const checkOut = new Date(bookingData.checkOut);
    const diffTime = Math.abs(checkOut.getTime() - checkIn.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  if (step === 'confirmation') {
    return (
      <section id="booking" className={cn('py-16 md:py-24 bg-gradient-to-br from-green-50 to-emerald-100', className)}>
        <div className="container mx-auto px-4 md:px-6">
          <FadeIn>
            <div className="max-w-2xl mx-auto text-center">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-10 h-10 text-green-600" />
              </div>
              <h2 className="text-3xl font-bold mb-4">Booking Request Submitted!</h2>
              <p className="text-lg text-muted-foreground mb-4">
                Thank you, {bookingData.name}! We have received your booking request for {calculateNights(new Date(bookingData.checkIn), new Date(bookingData.checkOut))} night{calculateNights(new Date(bookingData.checkIn), new Date(bookingData.checkOut)) !== 1 ? 's' : ''} in Tiruvannamalai.
              </p>

              {bookingConfirmation && (
                <div className="bg-primary/10 border border-primary/20 rounded-lg p-4 mb-6">
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">Booking Reference</p>
                    <p className="text-2xl font-bold text-primary">{bookingConfirmation.bookingReference}</p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Booking ID: {bookingConfirmation.bookingId}
                    </p>
                  </div>
                </div>
              )}

              <p className="text-sm text-muted-foreground mb-8">
                Our team will contact you within 24 hours to confirm your reservation and payment details.
              </p>
              
              <div className="bg-white/80 backdrop-blur-sm rounded-lg p-6 mb-8 text-left">
                <h3 className="font-semibold mb-4 text-center">Booking Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Check-in:</span>
                    <span className="font-medium">{new Date(bookingData.checkIn).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Check-out:</span>
                    <span className="font-medium">{new Date(bookingData.checkOut).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Adults:</span>
                    <span className="font-medium">{bookingData.adults}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Children:</span>
                    <span className="font-medium">{bookingData.children}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Rooms:</span>
                    <span className="font-medium">{bookingData.rooms}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Duration:</span>
                    <span className="font-medium">{calculateNights(new Date(bookingData.checkIn), new Date(bookingData.checkOut))} night{calculateNights(new Date(bookingData.checkIn), new Date(bookingData.checkOut)) !== 1 ? 's' : ''}</span>
                  </div>
                  {bookingConfirmation && (
                    <div className="flex justify-between border-t pt-2 mt-2">
                      <span className="font-semibold">Total Amount:</span>
                      <span className="font-bold text-primary">{formatCurrency(bookingConfirmation.totalAmount)}</span>
                    </div>
                  )}
                  {bookingData.roomPreference && (
                    <div className="flex justify-between">
                      <span>Room Preference:</span>
                      <span className="font-medium">{bookingData.roomPreference}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-4 justify-center">
                <Button onClick={resetForm} className="btn-primary">
                  <Calendar className="w-4 h-4" />
                  Make Another Booking
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/'} className="btn-outline">
                  <Home className="w-4 h-4" />
                  Back to Home
                </Button>
              </div>
            </div>
          </FadeIn>
        </div>
      </section>
    );
  }

  return (
    <section id="booking" className={cn('py-16 md:py-24 bg-gradient-to-br from-orange-50 to-red-50', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Book Your Stay in Tiruvannamalai
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Experience the spiritual heart of Tamil Nadu. Fill out the form below and we'll help you find the perfect accommodation.
            </p>
          </div>
        </FadeIn>

        <FadeIn delay={200}>
          <div className="max-w-4xl mx-auto">
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
                <CardTitle className="text-2xl font-serif text-center">Reservation Request</CardTitle>
                <p className="text-center text-primary-foreground/90">
                  Complete the form below to request your booking
                </p>
              </CardHeader>
              
              <CardContent className="p-8">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Date Selection */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="checkin" className="text-base font-medium">Check-in Date *</Label>
                      <div className="relative mt-2">
                        <Input
                          id="checkin"
                          type="date"
                          value={bookingData.checkIn}
                          onChange={(e) => handleInputChange('checkIn', e.target.value)}
                          className="pl-10"
                          min={new Date().toISOString().split('T')[0]}
                          required
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="checkout" className="text-base font-medium">Check-out Date *</Label>
                      <div className="relative mt-2">
                        <Input
                          id="checkout"
                          type="date"
                          value={bookingData.checkOut}
                          onChange={(e) => handleInputChange('checkOut', e.target.value)}
                          className="pl-10"
                          min={bookingData.checkIn || new Date().toISOString().split('T')[0]}
                          required
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      </div>
                    </div>
                  </div>

                  {/* Guests */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <Label htmlFor="adults" className="text-base font-medium">Adults</Label>
                      <Select value={bookingData.adults} onValueChange={(value) => handleInputChange('adults', value)}>
                        <SelectTrigger className="mt-2">
                          <Users className="w-4 h-4 mr-2" />
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 Adult</SelectItem>
                          <SelectItem value="2">2 Adults</SelectItem>
                          <SelectItem value="3">3 Adults</SelectItem>
                          <SelectItem value="4">4 Adults</SelectItem>
                          <SelectItem value="5">5 Adults</SelectItem>
                          <SelectItem value="6">6+ Adults</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="children" className="text-base font-medium">Children</Label>
                      <Select value={bookingData.children} onValueChange={(value) => handleInputChange('children', value)}>
                        <SelectTrigger className="mt-2">
                          <Users className="w-4 h-4 mr-2" />
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">No Children</SelectItem>
                          <SelectItem value="1">1 Child</SelectItem>
                          <SelectItem value="2">2 Children</SelectItem>
                          <SelectItem value="3">3 Children</SelectItem>
                          <SelectItem value="4">4+ Children</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="rooms" className="text-base font-medium">Number of Rooms</Label>
                      <Select value={bookingData.rooms} onValueChange={(value) => handleInputChange('rooms', value)}>
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 Room</SelectItem>
                          <SelectItem value="2">2 Rooms</SelectItem>
                          <SelectItem value="3">3 Rooms</SelectItem>
                          <SelectItem value="4">4 Rooms</SelectItem>
                          <SelectItem value="5">5+ Rooms</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Hotel and Room Preferences */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="hotelPreference" className="text-base font-medium">Hotel Preference (Optional)</Label>
                      <Select value={bookingData.hotelPreference} onValueChange={(value) => handleInputChange('hotelPreference', value)}>
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Select hotel preference" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="any">Any Available Hotel</SelectItem>
                          <SelectItem value="arunachala-palace">Arunachala Palace Hotel</SelectItem>
                          <SelectItem value="ramana-ashram">Ramana Ashram Guest House</SelectItem>
                          <SelectItem value="business-hotel">Business Hotel</SelectItem>
                          <SelectItem value="family-resort">Family Paradise Resort</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="roomPreference" className="text-base font-medium">Room Preference (Optional)</Label>
                      <Select value={bookingData.roomPreference} onValueChange={(value) => handleInputChange('roomPreference', value)}>
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Select room type preference" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="heritage-suite">Heritage Suite</SelectItem>
                          <SelectItem value="deluxe-room">Deluxe Room</SelectItem>
                          <SelectItem value="family-suite">Family Suite</SelectItem>
                          <SelectItem value="standard-room">Standard Room</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Guest Information */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Guest Information</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="name" className="text-base font-medium">Full Name *</Label>
                        <Input
                          id="name"
                          value={bookingData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="Enter your full name"
                          className="mt-2"
                          required
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="phone" className="text-base font-medium">Phone Number *</Label>
                        <div className="relative mt-2">
                          <Input
                            id="phone"
                            type="tel"
                            value={bookingData.phone}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            placeholder="+91 9876543210"
                            className="pl-10"
                            required
                          />
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-6">
                      <Label htmlFor="email" className="text-base font-medium">Email Address *</Label>
                      <div className="relative mt-2">
                        <Input
                          id="email"
                          type="email"
                          value={bookingData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                          className="pl-10"
                          required
                        />
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      </div>
                    </div>

                    <div className="mt-6">
                      <Label htmlFor="specialRequests" className="text-base font-medium">Special Requests (Optional)</Label>
                      <Textarea
                        id="specialRequests"
                        value={bookingData.specialRequests}
                        onChange={(e) => handleInputChange('specialRequests', e.target.value)}
                        placeholder="Any special requirements, dietary restrictions, or requests..."
                        rows={4}
                        className="mt-2"
                      />
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="pt-6">
                    <Button
                      type="submit"
                      className={cn("btn-primary w-full py-3 text-lg", loading && "btn-loading")}
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <Clock className="w-5 h-5 animate-spin" />
                          Submitting Request...
                        </>
                      ) : (
                        <>
                          <Calendar className="w-5 h-5" />
                          Submit Booking Request
                        </>
                      )}
                    </Button>
                    
                    <p className="text-sm text-muted-foreground text-center mt-3">
                      * We'll contact you within 24 hours to confirm availability and pricing
                    </p>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </FadeIn>

        {/* Contact Information */}
        <FadeIn delay={400}>
          <div className="mt-12 max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-serif text-center">Need Immediate Assistance?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div className="flex flex-col items-center gap-2">
                    <Phone className="w-8 h-8 text-primary" />
                    <div className="font-medium">Call Us</div>
                    <div className="text-sm text-muted-foreground">+91 4175 234567</div>
                    <div className="text-xs text-muted-foreground">Available 24/7</div>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <Mail className="w-8 h-8 text-primary" />
                    <div className="font-medium">Email Us</div>
                    <div className="text-sm text-muted-foreground"><EMAIL></div>
                    <div className="text-xs text-muted-foreground">Response within 2 hours</div>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <MapPin className="w-8 h-8 text-primary" />
                    <div className="font-medium">Visit Us</div>
                    <div className="text-sm text-muted-foreground">Tiruvannamalai, Tamil Nadu</div>
                    <div className="text-xs text-muted-foreground">Near Arunachaleswarar Temple</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default SimpleBooking;
