// Hotel service functions for Firebase operations
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Hotel, Room, Booking, HotelSearchFilters, BookingAvailability } from '@/types/hotel';

// Collections
const HOTELS_COLLECTION = 'hotels';
const ROOMS_COLLECTION = 'rooms';
const BOOKINGS_COLLECTION = 'bookings';

// Hotel Management Functions
export const createHotel = async (hotelData: Omit<Hotel, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const docRef = await addDoc(collection(db, HOTELS_COLLECTION), {
      ...hotelData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating hotel:', error);
    throw error;
  }
};

export const getHotel = async (hotelId: string): Promise<Hotel | null> => {
  try {
    const docRef = doc(db, HOTELS_COLLECTION, hotelId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Hotel;
    }
    return null;
  } catch (error) {
    console.error('Error getting hotel:', error);
    throw error;
  }
};

export const getAllHotels = async (): Promise<Hotel[]> => {
  try {
    const q = query(
      collection(db, HOTELS_COLLECTION),
      where('isActive', '==', true),
      orderBy('name')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Hotel[];
  } catch (error) {
    console.error('Error getting hotels:', error);
    throw error;
  }
};

export const getTiruvannamalaiHotels = async (): Promise<Hotel[]> => {
  try {
    const q = query(
      collection(db, HOTELS_COLLECTION),
      where('city', '==', 'Tiruvannamalai'),
      where('isActive', '==', true),
      orderBy('rating', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Hotel[];
  } catch (error) {
    console.error('Error getting Tiruvannamalai hotels:', error);
    throw error;
  }
};

export const updateHotel = async (hotelId: string, updates: Partial<Hotel>): Promise<void> => {
  try {
    const docRef = doc(db, HOTELS_COLLECTION, hotelId);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating hotel:', error);
    throw error;
  }
};

export const deleteHotel = async (hotelId: string): Promise<void> => {
  try {
    const docRef = doc(db, HOTELS_COLLECTION, hotelId);
    await updateDoc(docRef, {
      isActive: false,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error deleting hotel:', error);
    throw error;
  }
};

// Room Management Functions
export const createRoom = async (roomData: Omit<Room, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const docRef = await addDoc(collection(db, ROOMS_COLLECTION), {
      ...roomData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating room:', error);
    throw error;
  }
};

export const getHotelRooms = async (hotelId: string): Promise<Room[]> => {
  try {
    const q = query(
      collection(db, ROOMS_COLLECTION),
      where('hotelId', '==', hotelId),
      where('isActive', '==', true),
      orderBy('price')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Room[];
  } catch (error) {
    console.error('Error getting hotel rooms:', error);
    throw error;
  }
};

export const getRoom = async (roomId: string): Promise<Room | null> => {
  try {
    const docRef = doc(db, ROOMS_COLLECTION, roomId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Room;
    }
    return null;
  } catch (error) {
    console.error('Error getting room:', error);
    throw error;
  }
};

export const updateRoom = async (roomId: string, updates: Partial<Room>): Promise<void> => {
  try {
    const docRef = doc(db, ROOMS_COLLECTION, roomId);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating room:', error);
    throw error;
  }
};

// Booking Management Functions
export const createBooking = async (bookingData: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const batch = writeBatch(db);
    
    // Create booking
    const bookingRef = doc(collection(db, BOOKINGS_COLLECTION));
    batch.set(bookingRef, {
      ...bookingData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    
    // Update room availability
    const roomRef = doc(db, ROOMS_COLLECTION, bookingData.roomId);
    const roomDoc = await getDoc(roomRef);
    
    if (roomDoc.exists()) {
      const roomData = roomDoc.data() as Room;
      const newAvailableRooms = Math.max(0, roomData.availableRooms - bookingData.numberOfRooms);
      
      batch.update(roomRef, {
        availableRooms: newAvailableRooms,
        updatedAt: Timestamp.now()
      });
    }
    
    await batch.commit();
    return bookingRef.id;
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
};

export const getBooking = async (bookingId: string): Promise<Booking | null> => {
  try {
    const docRef = doc(db, BOOKINGS_COLLECTION, bookingId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Booking;
    }
    return null;
  } catch (error) {
    console.error('Error getting booking:', error);
    throw error;
  }
};

export const getHotelBookings = async (hotelId: string): Promise<Booking[]> => {
  try {
    const q = query(
      collection(db, BOOKINGS_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Booking[];
  } catch (error) {
    console.error('Error getting hotel bookings:', error);
    throw error;
  }
};

export const updateBookingStatus = async (bookingId: string, status: Booking['status']): Promise<void> => {
  try {
    const docRef = doc(db, BOOKINGS_COLLECTION, bookingId);
    await updateDoc(docRef, {
      status,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating booking status:', error);
    throw error;
  }
};

// Availability checking function
export const checkRoomAvailability = async (
  roomId: string,
  checkIn: Date,
  checkOut: Date,
  numberOfRooms: number = 1
): Promise<BookingAvailability> => {
  try {
    const room = await getRoom(roomId);
    if (!room) {
      return { roomId, available: false, availableRooms: 0, price: 0, totalPrice: 0 };
    }
    
    // Check for overlapping bookings
    const q = query(
      collection(db, BOOKINGS_COLLECTION),
      where('roomId', '==', roomId),
      where('status', 'in', ['confirmed', 'pending'])
    );
    
    const querySnapshot = await getDocs(q);
    let bookedRooms = 0;
    
    querySnapshot.docs.forEach(doc => {
      const booking = doc.data() as Booking;
      const bookingCheckIn = booking.checkInDate;
      const bookingCheckOut = booking.checkOutDate;
      
      // Check for date overlap
      if (checkIn < bookingCheckOut && checkOut > bookingCheckIn) {
        bookedRooms += booking.numberOfRooms;
      }
    });
    
    const availableRooms = room.totalRooms - bookedRooms;
    const isAvailable = availableRooms >= numberOfRooms;
    
    // Calculate total price (number of nights * room price * number of rooms)
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
    const totalPrice = nights * room.price * numberOfRooms;
    
    return {
      roomId,
      available: isAvailable,
      availableRooms,
      price: room.price,
      totalPrice
    };
  } catch (error) {
    console.error('Error checking room availability:', error);
    throw error;
  }
};
