rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions for authentication and authorization
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Simplified admin check - checks if admin document exists
    function isAdmin() {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    // Check if user is super admin
    function isSuperAdmin() {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/admins/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.role == 'super_admin';
    }

    // Check admin emails directly (for initial setup)
    function isAdminEmail() {
      return isAuthenticated() &&
             request.auth.token.email in [
               '<EMAIL>',
               '<EMAIL>',
               '<EMAIL>',
               '<EMAIL>'
             ];
    }

    // Users collection - User profile management
    match /users/{userId} {
      // Users can read and write their own profile
      allow read, write: if isOwner(userId);

      // Admins can read all user profiles
      allow read: if isAdmin() || isAdminEmail();

      // Super admins can manage all users
      allow write: if isSuperAdmin() || isAdminEmail();
    }

    // Admins collection - Admin management
    match /admins/{adminId} {
      // Allow admin emails to create their own admin documents
      allow create: if isAuthenticated() &&
                       request.auth.uid == adminId &&
                       isAdminEmail();

      // Admins can read their own profile
      allow read: if isOwner(adminId);

      // Super admins can manage all admin accounts
      allow read, write: if isSuperAdmin() || isAdminEmail();

      // Admins can read other admin profiles
      allow read: if isAdmin();
    }

    // Hotels collection - Hotel management
    match /hotels/{hotelId} {
      // Anyone can read hotel information (public data)
      allow read: if true;

      // Admins can create/update hotels
      allow create, update: if isAdmin() || isAdminEmail();

      // Super admins can delete hotels
      allow delete: if isSuperAdmin() || isAdminEmail();
    }

    // Bookings collection - Booking management
    match /bookings/{bookingId} {
      // Users can read their own bookings (multiple conditions for flexibility)
      allow read: if isAuthenticated() && (
        // If userId matches the authenticated user
        resource.data.userId == request.auth.uid ||
        // If guestEmail matches the authenticated user's email
        resource.data.guestEmail == request.auth.token.email ||
        // If the booking was created by this user (fallback)
        resource.data.createdBy == request.auth.uid
      );

      // Authenticated users can create bookings
      allow create: if isAuthenticated() && (
        // User can create booking for themselves
        request.resource.data.userId == request.auth.uid ||
        // Or if guestEmail matches their email
        request.resource.data.guestEmail == request.auth.token.email ||
        // Or if they're setting themselves as creator
        request.resource.data.createdBy == request.auth.uid
      );

      // Users can update their own bookings (limited fields)
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        resource.data.guestEmail == request.auth.token.email ||
        resource.data.createdBy == request.auth.uid
      ) && (
        // Only allow updating specific fields
        request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['specialRequests', 'guestNotes', 'updatedAt', 'status'])
      );

      // Admins can read and manage all bookings
      allow read, write: if isAdmin() || isAdminEmail();
    }

    // Hotel bookings collection - Quick hotel booking queries
    match /hotel_bookings/{hotelBookingId} {
      // Admins can read and manage hotel bookings
      allow read, write: if isAdmin() || isAdminEmail();
    }

    // Booking statistics collection - Analytics
    match /booking_stats/{statsId} {
      // Admins can read statistics
      allow read: if isAdmin() || isAdminEmail();

      // Admins can write statistics
      allow write: if isAdmin() || isAdminEmail();
    }

    // Notifications collection - User notifications
    match /notifications/{notificationId} {
      // Users can read their own notifications
      allow read: if isAuthenticated() &&
                     resource.data.userId == request.auth.uid;

      // Users can update their own notifications (mark as read)
      allow update: if isAuthenticated() &&
                       resource.data.userId == request.auth.uid;

      // Admins can create and manage notifications
      allow create, read: if isAdmin() || isAdminEmail();
    }

    // Settings collection - Application settings
    match /settings/{settingId} {
      // Anyone can read public settings
      allow read: if true;

      // Admins can manage settings
      allow write: if isAdmin() || isAdminEmail();
    }

    // Development fallback - allows authenticated users to access data
    match /{document=**} {
      allow read, write: if isAuthenticated();
    }
  }
}
