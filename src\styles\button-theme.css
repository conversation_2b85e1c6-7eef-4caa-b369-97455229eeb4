/* Consistent Button Theme Styles */

/* Primary Button Theme */
.btn-primary {
  @apply relative bg-gradient-to-r from-primary to-primary/80 text-white px-6 py-3 rounded-lg font-medium;
  @apply hover:from-primary/90 hover:to-primary/70 hover:shadow-lg hover:scale-105;
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center justify-center gap-2;
}

/* Secondary Button Theme */
.btn-secondary {
  @apply relative bg-gradient-to-r from-gray-100 to-gray-50 text-gray-700 px-6 py-3 rounded-lg font-medium border border-gray-200;
  @apply hover:bg-white hover:border-primary/30 hover:text-primary hover:shadow-md hover:scale-105;
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center justify-center gap-2;
}

/* Accent Button Theme */
.btn-accent {
  @apply relative bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-lg font-medium;
  @apply hover:from-orange-400 hover:to-red-400 hover:shadow-lg hover:scale-105;
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center justify-center gap-2;
}

/* Success Button Theme */
.btn-success {
  @apply relative bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-3 rounded-lg font-medium;
  @apply hover:from-green-400 hover:to-emerald-400 hover:shadow-lg hover:scale-105;
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center justify-center gap-2;
}

/* Warning Button Theme */
.btn-warning {
  @apply relative bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-6 py-3 rounded-lg font-medium;
  @apply hover:from-yellow-400 hover:to-orange-400 hover:shadow-lg hover:scale-105;
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center justify-center gap-2;
}

/* Outline Button Theme */
.btn-outline {
  @apply relative bg-transparent text-primary px-6 py-3 rounded-lg font-medium border-2 border-primary/30;
  @apply hover:bg-primary/5 hover:border-primary hover:shadow-md hover:scale-105;
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center justify-center gap-2;
}

/* Ghost Button Theme */
.btn-ghost {
  @apply relative bg-transparent text-gray-700 px-6 py-3 rounded-lg font-medium;
  @apply hover:bg-gray-100 hover:text-primary hover:scale-105;
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center justify-center gap-2;
}

/* Small Button Variants */
.btn-sm {
  @apply px-4 py-2 text-sm;
}

/* Large Button Variants */
.btn-lg {
  @apply px-8 py-4 text-lg;
}

/* Icon Button Theme */
.btn-icon {
  @apply w-10 h-10 p-0 rounded-lg flex items-center justify-center;
  @apply bg-white/80 backdrop-blur-sm border border-gray-200/50;
  @apply hover:bg-white hover:shadow-lg hover:scale-110;
  @apply transition-all duration-300 ease-in-out;
}

/* Navigation Button Theme */
.btn-nav {
  @apply relative text-sm font-medium text-gray-700 px-3 py-2 rounded-lg;
  @apply hover:text-primary hover:bg-primary/5 hover:scale-105;
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center gap-2;
}

/* Mobile Menu Button Theme */
.btn-mobile-menu {
  @apply text-left p-4 rounded-xl bg-white/80 backdrop-blur-sm border border-gray-200/50;
  @apply hover:bg-white hover:border-primary/30 hover:text-primary hover:scale-[1.02] hover:shadow-lg;
  @apply transition-all duration-300 ease-in-out shadow-sm;
}

/* Card Button Theme */
.btn-card {
  @apply w-full bg-gradient-to-r from-primary to-primary/80 text-white py-3 rounded-lg font-medium;
  @apply hover:from-primary/90 hover:to-primary/70 hover:shadow-lg hover:scale-[1.02];
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center justify-center gap-2;
}

/* Filter Button Theme */
.btn-filter {
  @apply px-4 py-2 rounded-full font-medium text-sm border;
  @apply transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-lg;
  @apply flex items-center gap-2;
}

.btn-filter.active {
  @apply bg-gradient-to-r from-primary to-primary/80 text-white border-primary shadow-lg;
}

.btn-filter.inactive {
  @apply bg-white/80 backdrop-blur-sm text-gray-700 border-gray-200/50 hover:bg-white hover:border-primary/30;
}

/* Floating Action Button */
.btn-fab {
  @apply fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-lg;
  @apply bg-gradient-to-r from-primary to-primary/80 text-white;
  @apply hover:shadow-xl hover:scale-110 hover:from-primary/90 hover:to-primary/70;
  @apply transition-all duration-300 ease-in-out;
  @apply flex items-center justify-center;
  @apply z-50;
}

/* Loading Button State */
.btn-loading {
  @apply opacity-75 cursor-not-allowed;
}

.btn-loading::after {
  content: '';
  @apply w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin ml-2;
}

/* Button Ripple Effect */
.btn-ripple {
  @apply relative overflow-hidden;
}

.btn-ripple::before {
  content: '';
  @apply absolute inset-0 bg-white/20 scale-0 rounded-full;
  @apply transition-transform duration-300 ease-out;
}

.btn-ripple:active::before {
  @apply scale-100;
}

/* Disabled Button State */
.btn-disabled {
  @apply opacity-50 cursor-not-allowed;
  @apply hover:scale-100 hover:shadow-none;
}

/* Button Group */
.btn-group {
  @apply flex rounded-lg overflow-hidden border border-gray-200;
}

.btn-group .btn-group-item {
  @apply border-0 rounded-none border-r border-gray-200 last:border-r-0;
  @apply hover:bg-primary/5 hover:text-primary;
}

/* Responsive Button Adjustments */
@media (max-width: 768px) {
  .btn-responsive {
    @apply px-4 py-2 text-sm;
  }
  
  .btn-responsive.btn-lg {
    @apply px-6 py-3 text-base;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .btn-secondary {
    @apply from-gray-800 to-gray-700 text-gray-200 border-gray-600;
    @apply hover:bg-gray-700 hover:border-primary/50;
  }
  
  .btn-ghost {
    @apply text-gray-300 hover:bg-gray-800;
  }
  
  .btn-nav {
    @apply text-gray-300 hover:bg-primary/10;
  }
}
