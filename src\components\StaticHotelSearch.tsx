// Static hotel search component that works without Firebase
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Calendar, 
  Users, 
  MapPin, 
  Filter,
  Star,
  IndianRupee,
  Bed,
  Wifi,
  Car,
  Utensils
} from 'lucide-react';
import { toast } from 'sonner';

interface SearchFilters {
  location: string;
  checkIn: string;
  checkOut: string;
  guests: string;
  priceRange: string;
  amenities: string[];
  rating: string;
}

interface StaticHotelSearchProps {
  className?: string;
  onHotelSelect?: (hotel: any) => void;
}

const StaticHotelSearch: React.FC<StaticHotelSearchProps> = ({ className, onHotelSelect }) => {
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    location: '',
    checkIn: '',
    checkOut: '',
    guests: '2',
    priceRange: '',
    amenities: [],
    rating: ''
  });

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const handleFilterChange = (field: keyof SearchFilters, value: string | string[]) => {
    setSearchFilters(prev => ({ ...prev, [field]: value }));
  };

  const handleSearch = () => {
    // Validate required fields
    if (!searchFilters.checkIn || !searchFilters.checkOut) {
      toast.error('Please select check-in and check-out dates');
      return;
    }

    // Validate dates
    const checkInDate = new Date(searchFilters.checkIn);
    const checkOutDate = new Date(searchFilters.checkOut);
    
    if (checkInDate >= checkOutDate) {
      toast.error('Check-out date must be after check-in date');
      return;
    }

    if (checkInDate < new Date()) {
      toast.error('Check-in date cannot be in the past');
      return;
    }

    // Simulate search
    toast.success('Searching for hotels... Scroll down to see available hotels!');
    
    // Scroll to hotels section
    const hotelsSection = document.getElementById('hotels');
    if (hotelsSection) {
      hotelsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const popularSearches = [
    'Near Arunachaleswarar Temple',
    'Budget Hotels',
    'Luxury Hotels',
    'Family Hotels',
    'Business Hotels'
  ];

  const amenityOptions = [
    { id: 'wifi', label: 'Free WiFi', icon: <Wifi className="w-4 h-4" /> },
    { id: 'parking', label: 'Parking', icon: <Car className="w-4 h-4" /> },
    { id: 'restaurant', label: 'Restaurant', icon: <Utensils className="w-4 h-4" /> },
    { id: 'ac', label: 'Air Conditioning', icon: <Star className="w-4 h-4" /> }
  ];

  return (
    <section id="search" className={cn('py-16 md:py-24 bg-gradient-to-br from-orange-50 to-red-50', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Find Your Perfect Stay in Tiruvannamalai
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Search and compare hotels near the sacred Arunachaleswarar Temple. 
              Find the perfect accommodation for your spiritual journey.
            </p>
          </div>
        </FadeIn>

        <FadeIn delay={200}>
          <Card className="max-w-4xl mx-auto overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
              <CardTitle className="text-2xl font-serif text-center flex items-center justify-center gap-2">
                <Search className="w-6 h-6" />
                Hotel Search
              </CardTitle>
            </CardHeader>
            
            <CardContent className="p-8">
              {/* Main Search Form */}
              <div className="space-y-6">
                {/* Location and Dates */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor="location" className="text-base font-medium">Location</Label>
                    <div className="relative mt-2">
                      <Input
                        id="location"
                        value={searchFilters.location}
                        onChange={(e) => handleFilterChange('location', e.target.value)}
                        placeholder="Tiruvannamalai"
                        className="pl-10"
                      />
                      <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="checkin" className="text-base font-medium">Check-in</Label>
                    <div className="relative mt-2">
                      <Input
                        id="checkin"
                        type="date"
                        value={searchFilters.checkIn}
                        onChange={(e) => handleFilterChange('checkIn', e.target.value)}
                        className="pl-10"
                        min={new Date().toISOString().split('T')[0]}
                      />
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="checkout" className="text-base font-medium">Check-out</Label>
                    <div className="relative mt-2">
                      <Input
                        id="checkout"
                        type="date"
                        value={searchFilters.checkOut}
                        onChange={(e) => handleFilterChange('checkOut', e.target.value)}
                        className="pl-10"
                        min={searchFilters.checkIn || new Date().toISOString().split('T')[0]}
                      />
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="guests" className="text-base font-medium">Guests</Label>
                    <Select value={searchFilters.guests} onValueChange={(value) => handleFilterChange('guests', value)}>
                      <SelectTrigger className="mt-2">
                        <Users className="w-4 h-4 mr-2" />
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 Guest</SelectItem>
                        <SelectItem value="2">2 Guests</SelectItem>
                        <SelectItem value="3">3 Guests</SelectItem>
                        <SelectItem value="4">4 Guests</SelectItem>
                        <SelectItem value="5">5 Guests</SelectItem>
                        <SelectItem value="6">6+ Guests</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Advanced Filters Toggle */}
                <div className="flex justify-center">
                  <Button
                    variant="outline"
                    onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    className="flex items-center gap-2"
                  >
                    <Filter className="w-4 h-4" />
                    {showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters
                  </Button>
                </div>

                {/* Advanced Filters */}
                {showAdvancedFilters && (
                  <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="priceRange" className="text-base font-medium">Price Range</Label>
                        <Select value={searchFilters.priceRange} onValueChange={(value) => handleFilterChange('priceRange', value)}>
                          <SelectTrigger className="mt-2">
                            <IndianRupee className="w-4 h-4 mr-2" />
                            <SelectValue placeholder="Any price" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="budget">Budget (₹1,000 - ₹3,000)</SelectItem>
                            <SelectItem value="mid">Mid-range (₹3,000 - ₹6,000)</SelectItem>
                            <SelectItem value="luxury">Luxury (₹6,000+)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label htmlFor="rating" className="text-base font-medium">Minimum Rating</Label>
                        <Select value={searchFilters.rating} onValueChange={(value) => handleFilterChange('rating', value)}>
                          <SelectTrigger className="mt-2">
                            <Star className="w-4 h-4 mr-2" />
                            <SelectValue placeholder="Any rating" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="4.5">4.5+ Stars</SelectItem>
                            <SelectItem value="4.0">4.0+ Stars</SelectItem>
                            <SelectItem value="3.5">3.5+ Stars</SelectItem>
                            <SelectItem value="3.0">3.0+ Stars</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Amenities */}
                    <div>
                      <Label className="text-base font-medium">Amenities</Label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                        {amenityOptions.map((amenity) => (
                          <label key={amenity.id} className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={searchFilters.amenities.includes(amenity.id)}
                              onChange={(e) => {
                                const newAmenities = e.target.checked
                                  ? [...searchFilters.amenities, amenity.id]
                                  : searchFilters.amenities.filter(a => a !== amenity.id);
                                handleFilterChange('amenities', newAmenities);
                              }}
                              className="rounded"
                            />
                            {amenity.icon}
                            <span className="text-sm">{amenity.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Search Button */}
                <div className="flex justify-center">
                  <Button 
                    onClick={handleSearch}
                    className="btn-primary px-8 py-3 text-lg"
                  >
                    <Search className="w-5 h-5 mr-2" />
                    Search Hotels
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </FadeIn>

        {/* Popular Searches */}
        <FadeIn delay={400}>
          <div className="max-w-4xl mx-auto mt-8">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold">Popular Searches</h3>
            </div>
            <div className="flex flex-wrap justify-center gap-2">
              {popularSearches.map((search, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="rounded-full"
                  onClick={() => {
                    handleFilterChange('location', search);
                    toast.info(`Searching for: ${search}`);
                  }}
                >
                  {search}
                </Button>
              ))}
            </div>
          </div>
        </FadeIn>

        {/* Quick Stats */}
        <FadeIn delay={500}>
          <div className="max-w-4xl mx-auto mt-12">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-white/80 backdrop-blur-sm rounded-lg">
                <div className="text-2xl font-bold text-primary">5+</div>
                <div className="text-sm text-muted-foreground">Premium Hotels</div>
              </div>
              <div className="text-center p-4 bg-white/80 backdrop-blur-sm rounded-lg">
                <div className="text-2xl font-bold text-primary">50+</div>
                <div className="text-sm text-muted-foreground">Room Types</div>
              </div>
              <div className="text-center p-4 bg-white/80 backdrop-blur-sm rounded-lg">
                <div className="text-2xl font-bold text-primary">4.6</div>
                <div className="text-sm text-muted-foreground">Average Rating</div>
              </div>
              <div className="text-center p-4 bg-white/80 backdrop-blur-sm rounded-lg">
                <div className="text-2xl font-bold text-primary">1000+</div>
                <div className="text-sm text-muted-foreground">Happy Guests</div>
              </div>
            </div>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default StaticHotelSearch;
