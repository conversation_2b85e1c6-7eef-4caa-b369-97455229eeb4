// Enhanced booking component with Firebase integration
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  Users, 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  IndianRupee,
  Bed
} from 'lucide-react';
import { toast } from 'sonner';
import { getTiruvannamalaiHotels, getHotelRooms, checkRoomAvailability, createBooking } from '@/services/hotelService';
import { notifyBookingConfirmed, notifyAdminNewBooking } from '@/services/realtimeService';
import { Hotel, Room, BookingAvailability } from '@/types/hotel';

interface EnhancedBookingProps {
  className?: string;
  selectedHotelId?: string;
  selectedRoomId?: string;
}

const EnhancedBooking: React.FC<EnhancedBookingProps> = ({ 
  className, 
  selectedHotelId, 
  selectedRoomId 
}) => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [availability, setAvailability] = useState<BookingAvailability[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [step, setStep] = useState<'search' | 'select' | 'details' | 'confirmation'>('search');
  
  const [searchData, setSearchData] = useState({
    checkIn: '',
    checkOut: '',
    guests: '2',
    hotelId: selectedHotelId || '',
    roomId: selectedRoomId || ''
  });

  const [bookingData, setBookingData] = useState({
    name: '',
    email: '',
    phone: '',
    specialRequests: '',
    numberOfRooms: 1
  });

  const [selectedAvailability, setSelectedAvailability] = useState<BookingAvailability | null>(null);

  // Load hotels on component mount
  useEffect(() => {
    loadHotels();
  }, []);

  // Load rooms when hotel is selected
  useEffect(() => {
    if (searchData.hotelId) {
      loadRooms(searchData.hotelId);
    }
  }, [searchData.hotelId]);

  const loadHotels = async () => {
    try {
      const hotelsData = await getTiruvannamalaiHotels();
      setHotels(hotelsData);
    } catch (error) {
      console.error('Error loading hotels:', error);
      toast.error('Failed to load hotels');
    }
  };

  const loadRooms = async (hotelId: string) => {
    try {
      const roomsData = await getHotelRooms(hotelId);
      setRooms(roomsData);
    } catch (error) {
      console.error('Error loading rooms:', error);
      toast.error('Failed to load rooms');
    }
  };

  const handleSearchInputChange = (field: string, value: string) => {
    setSearchData(prev => ({ ...prev, [field]: value }));
  };

  const handleBookingInputChange = (field: string, value: string | number) => {
    setBookingData(prev => ({ ...prev, [field]: value }));
  };

  const checkAvailability = async () => {
    if (!searchData.checkIn || !searchData.checkOut || !searchData.hotelId) {
      toast.error('Please fill in all search fields');
      return;
    }

    const checkInDate = new Date(searchData.checkIn);
    const checkOutDate = new Date(searchData.checkOut);
    
    if (checkInDate >= checkOutDate) {
      toast.error('Check-out date must be after check-in date');
      return;
    }

    if (checkInDate < new Date()) {
      toast.error('Check-in date cannot be in the past');
      return;
    }

    setLoading(true);
    try {
      const availabilityPromises = rooms.map(room => 
        checkRoomAvailability(room.id, checkInDate, checkOutDate, bookingData.numberOfRooms)
      );
      
      const availabilityResults = await Promise.all(availabilityPromises);
      setAvailability(availabilityResults);
      setStep('select');
    } catch (error) {
      console.error('Error checking availability:', error);
      toast.error('Failed to check availability');
    } finally {
      setLoading(false);
    }
  };

  const selectRoom = (roomAvailability: BookingAvailability) => {
    setSelectedAvailability(roomAvailability);
    setStep('details');
  };

  const submitBooking = async () => {
    if (!selectedAvailability || !bookingData.name || !bookingData.email || !bookingData.phone) {
      toast.error('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    try {
      const checkInDate = new Date(searchData.checkIn);
      const checkOutDate = new Date(searchData.checkOut);
      
      const booking = {
        hotelId: searchData.hotelId,
        roomId: selectedAvailability.roomId,
        guestInfo: {
          name: bookingData.name,
          email: bookingData.email,
          phone: bookingData.phone
        },
        checkInDate,
        checkOutDate,
        numberOfGuests: parseInt(searchData.guests),
        numberOfRooms: bookingData.numberOfRooms,
        totalAmount: selectedAvailability.totalPrice,
        status: 'pending' as const,
        paymentStatus: 'pending' as const,
        specialRequests: bookingData.specialRequests,
        bookingSource: 'website' as const
      };

      const bookingId = await createBooking(booking);

      // Send real-time notifications
      try {
        // Create the booking object with the new ID for notifications
        const bookingWithId = { ...booking, id: bookingId };

        // Notify user (if they have an account)
        if (bookingWithId.userId) {
          await notifyBookingConfirmed(bookingWithId);
        }

        // Notify admin (you might want to get admin IDs from your admin collection)
        // For now, we'll skip this as we'd need to query for admins
        // await notifyAdminNewBooking(bookingWithId, 'admin-id');
      } catch (notificationError) {
        console.error('Error sending notifications:', notificationError);
        // Don't fail the booking if notifications fail
      }

      toast.success('Booking submitted successfully!');
      setStep('confirmation');
    } catch (error) {
      console.error('Error submitting booking:', error);
      toast.error('Failed to submit booking. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const resetBooking = () => {
    setStep('search');
    setAvailability([]);
    setSelectedAvailability(null);
    setBookingData({
      name: '',
      email: '',
      phone: '',
      specialRequests: '',
      numberOfRooms: 1
    });
  };

  const selectedHotel = hotels.find(h => h.id === searchData.hotelId);
  const selectedRoom = rooms.find(r => r.id === selectedAvailability?.roomId);

  return (
    <section id="booking" className={cn('py-16 md:py-24 bg-background', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Book Your Stay in Tiruvannamalai
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Experience the spiritual heart of Tamil Nadu with our carefully selected hotels
            </p>
          </div>
        </FadeIn>

        <div className="max-w-4xl mx-auto">
          {step === 'search' && (
            <FadeIn>
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-serif">Search Available Rooms</CardTitle>
                  <p className="text-muted-foreground">Find the perfect accommodation for your dates</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="checkin">Check-in Date</Label>
                      <div className="relative">
                        <Input
                          id="checkin"
                          type="date"
                          value={searchData.checkIn}
                          onChange={(e) => handleSearchInputChange('checkIn', e.target.value)}
                          className="pl-10"
                          min={new Date().toISOString().split('T')[0]}
                          required
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="checkout">Check-out Date</Label>
                      <div className="relative">
                        <Input
                          id="checkout"
                          type="date"
                          value={searchData.checkOut}
                          onChange={(e) => handleSearchInputChange('checkOut', e.target.value)}
                          className="pl-10"
                          min={searchData.checkIn || new Date().toISOString().split('T')[0]}
                          required
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="guests">Number of Guests</Label>
                      <Select value={searchData.guests} onValueChange={(value) => handleSearchInputChange('guests', value)}>
                        <SelectTrigger>
                          <Users className="w-4 h-4 mr-2" />
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 Guest</SelectItem>
                          <SelectItem value="2">2 Guests</SelectItem>
                          <SelectItem value="3">3 Guests</SelectItem>
                          <SelectItem value="4">4 Guests</SelectItem>
                          <SelectItem value="5">5+ Guests</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="hotel">Select Hotel</Label>
                      <Select value={searchData.hotelId} onValueChange={(value) => handleSearchInputChange('hotelId', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a hotel" />
                        </SelectTrigger>
                        <SelectContent>
                          {hotels.map((hotel) => (
                            <SelectItem key={hotel.id} value={hotel.id}>
                              {hotel.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="numberOfRooms">Number of Rooms</Label>
                    <Select 
                      value={bookingData.numberOfRooms.toString()} 
                      onValueChange={(value) => handleBookingInputChange('numberOfRooms', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 Room</SelectItem>
                        <SelectItem value="2">2 Rooms</SelectItem>
                        <SelectItem value="3">3 Rooms</SelectItem>
                        <SelectItem value="4">4 Rooms</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button 
                    onClick={checkAvailability} 
                    className="w-full" 
                    disabled={loading}
                  >
                    {loading ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Checking Availability...
                      </div>
                    ) : (
                      'Check Availability'
                    )}
                  </Button>
                </CardContent>
              </Card>
            </FadeIn>
          )}

          {step === 'select' && (
            <FadeIn>
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-serif">Available Rooms</CardTitle>
                  <p className="text-muted-foreground">
                    {selectedHotel?.name} - {searchData.checkIn} to {searchData.checkOut}
                  </p>
                </CardHeader>
                <CardContent>
                  {availability.filter(a => a.available).length === 0 ? (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        No rooms available for your selected dates. Please try different dates or another hotel.
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <div className="space-y-4">
                      {availability
                        .filter(a => a.available)
                        .map((roomAvailability) => {
                          const room = rooms.find(r => r.id === roomAvailability.roomId);
                          if (!room) return null;

                          return (
                            <Card key={room.id} className="border-2 hover:border-primary/50 transition-colors">
                              <CardContent className="p-6">
                                <div className="flex justify-between items-start">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-3 mb-2">
                                      <h3 className="text-xl font-semibold">{room.name}</h3>
                                      <Badge variant="outline" className="capitalize">
                                        {room.type}
                                      </Badge>
                                    </div>
                                    <p className="text-muted-foreground mb-4">{room.description}</p>

                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                                      <div className="flex items-center gap-2">
                                        <Users className="w-4 h-4 text-muted-foreground" />
                                        <span>Up to {room.capacity} guests</span>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <Bed className="w-4 h-4 text-muted-foreground" />
                                        <span>{room.bedType}</span>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <MapPin className="w-4 h-4 text-muted-foreground" />
                                        <span>{room.size}</span>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                        <span>{roomAvailability.availableRooms} available</span>
                                      </div>
                                    </div>

                                    <div className="flex flex-wrap gap-2 mb-4">
                                      {room.amenities.slice(0, 4).map((amenity, index) => (
                                        <Badge key={index} variant="outline" className="text-xs">
                                          {amenity}
                                        </Badge>
                                      ))}
                                      {room.amenities.length > 4 && (
                                        <Badge variant="outline" className="text-xs">
                                          +{room.amenities.length - 4} more
                                        </Badge>
                                      )}
                                    </div>
                                  </div>

                                  <div className="text-right ml-6">
                                    <div className="text-2xl font-bold text-primary mb-1">
                                      ₹{roomAvailability.totalPrice.toLocaleString()}
                                    </div>
                                    <div className="text-sm text-muted-foreground mb-4">
                                      ₹{roomAvailability.price}/night × {bookingData.numberOfRooms} room(s)
                                    </div>
                                    <Button onClick={() => selectRoom(roomAvailability)}>
                                      Select Room
                                    </Button>
                                  </div>
                                </div>

                                {room.images && room.images.length > 0 && (
                                  <div className="mt-4 flex gap-2 overflow-x-auto">
                                    {room.images.slice(0, 4).map((image, index) => (
                                      <img
                                        key={index}
                                        src={image}
                                        alt={`${room.name} ${index + 1}`}
                                        className="w-20 h-20 object-cover rounded border flex-shrink-0"
                                      />
                                    ))}
                                  </div>
                                )}
                              </CardContent>
                            </Card>
                          );
                        })}
                    </div>
                  )}

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setStep('search')}>
                      Back to Search
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </FadeIn>
          )}

          {step === 'details' && selectedAvailability && selectedRoom && (
            <FadeIn>
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-serif">Booking Details</CardTitle>
                  <p className="text-muted-foreground">Complete your reservation</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Booking Summary */}
                  <Card className="bg-muted/50">
                    <CardContent className="p-4">
                      <h3 className="font-semibold mb-3">Booking Summary</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Hotel:</span>
                          <span className="font-medium">{selectedHotel?.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Room:</span>
                          <span className="font-medium">{selectedRoom.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Check-in:</span>
                          <span className="font-medium">{searchData.checkIn}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Check-out:</span>
                          <span className="font-medium">{searchData.checkOut}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Guests:</span>
                          <span className="font-medium">{searchData.guests}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Rooms:</span>
                          <span className="font-medium">{bookingData.numberOfRooms}</span>
                        </div>
                        <div className="border-t pt-2 mt-2">
                          <div className="flex justify-between font-semibold">
                            <span>Total Amount:</span>
                            <span className="text-primary">₹{selectedAvailability.totalPrice.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Guest Information */}
                  <div className="space-y-4">
                    <h3 className="font-semibold">Guest Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Full Name *</Label>
                        <Input
                          id="name"
                          value={bookingData.name}
                          onChange={(e) => handleBookingInputChange('name', e.target.value)}
                          placeholder="Enter your full name"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone">Phone Number *</Label>
                        <div className="relative">
                          <Input
                            id="phone"
                            type="tel"
                            value={bookingData.phone}
                            onChange={(e) => handleBookingInputChange('phone', e.target.value)}
                            placeholder="Enter your phone number"
                            className="pl-10"
                            required
                          />
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="email">Email Address *</Label>
                      <div className="relative">
                        <Input
                          id="email"
                          type="email"
                          value={bookingData.email}
                          onChange={(e) => handleBookingInputChange('email', e.target.value)}
                          placeholder="Enter your email address"
                          className="pl-10"
                          required
                        />
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="specialRequests">Special Requests (Optional)</Label>
                      <Textarea
                        id="specialRequests"
                        value={bookingData.specialRequests}
                        onChange={(e) => handleBookingInputChange('specialRequests', e.target.value)}
                        placeholder="Any special requests or requirements..."
                        rows={3}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between gap-4">
                    <Button variant="outline" onClick={() => setStep('select')}>
                      Back to Rooms
                    </Button>
                    <Button onClick={submitBooking} disabled={submitting}>
                      {submitting ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          Submitting...
                        </div>
                      ) : (
                        'Confirm Booking'
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </FadeIn>
          )}

          {step === 'confirmation' && (
            <FadeIn>
              <Card>
                <CardContent className="text-center py-12">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold mb-4">Booking Confirmed!</h2>
                  <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                    Thank you for your booking. We have received your reservation request and will contact you shortly to confirm the details.
                  </p>

                  <div className="bg-muted/50 rounded-lg p-6 mb-6 max-w-md mx-auto">
                    <h3 className="font-semibold mb-3">Booking Details</h3>
                    <div className="space-y-2 text-sm text-left">
                      <div className="flex justify-between">
                        <span>Hotel:</span>
                        <span className="font-medium">{selectedHotel?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Room:</span>
                        <span className="font-medium">{selectedRoom?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Guest:</span>
                        <span className="font-medium">{bookingData.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Check-in:</span>
                        <span className="font-medium">{searchData.checkIn}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Check-out:</span>
                        <span className="font-medium">{searchData.checkOut}</span>
                      </div>
                      <div className="border-t pt-2 mt-2">
                        <div className="flex justify-between font-semibold">
                          <span>Total:</span>
                          <span className="text-primary">₹{selectedAvailability?.totalPrice.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Alert className="max-w-md mx-auto mb-6">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      A confirmation email will be sent to {bookingData.email} with your booking details.
                    </AlertDescription>
                  </Alert>

                  <div className="flex gap-4 justify-center">
                    <Button onClick={resetBooking}>
                      Make Another Booking
                    </Button>
                    <Button variant="outline" onClick={() => window.location.href = '/'}>
                      Back to Home
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </FadeIn>
          )}
        </div>

        {/* Contact Information */}
        <FadeIn delay={200}>
          <div className="mt-12 max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-serif">Need Help?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div className="flex flex-col items-center gap-2">
                    <Phone className="w-6 h-6 text-primary" />
                    <div className="font-medium">Call Us</div>
                    <div className="text-sm text-muted-foreground">+91 4175 234567</div>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <Mail className="w-6 h-6 text-primary" />
                    <div className="font-medium">Email Us</div>
                    <div className="text-sm text-muted-foreground"><EMAIL></div>
                  </div>
                  <div className="flex flex-col items-center gap-2">
                    <Clock className="w-6 h-6 text-primary" />
                    <div className="font-medium">Support Hours</div>
                    <div className="text-sm text-muted-foreground">24/7 Available</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default EnhancedBooking;
