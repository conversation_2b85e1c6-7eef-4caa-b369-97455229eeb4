// Quick admin creation script - paste this directly in browser console
// Copy and paste this entire script into your browser console

(async function createTamilAdminQuick() {
  console.log('🚀 Starting Tamil Admin Creation...');
  
  try {
    // Import Firebase functions
    const { createUserWithEmailAndPassword, updateProfile } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
    const { doc, setDoc } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
    
    // Get Firebase instances (assuming they're available globally)
    const auth = window.firebase?.auth?.() || window.auth;
    const db = window.firebase?.firestore?.() || window.db;
    
    if (!auth || !db) {
      console.error('❌ Firebase not properly initialized');
      console.log('🔧 Please ensure Firebase is loaded');
      return;
    }
    
    const adminData = {
      email: '<EMAIL>',
      password: 'tamiladmin',
      name: 'Tamil Administrator',
      role: 'super_admin',
      permissions: [
        'manage_users',
        'manage_admins',
        'manage_hotels',
        'manage_bookings',
        'view_analytics',
        'manage_settings',
        'manage_payments',
        'manage_reviews',
        'system_admin'
      ]
    };

    console.log('📧 Creating user:', adminData.email);
    
    // Create Firebase user
    const userCredential = await createUserWithEmailAndPassword(
      auth, 
      adminData.email, 
      adminData.password
    );
    const user = userCredential.user;
    console.log('✅ Firebase user created with UID:', user.uid);

    // Update profile
    await updateProfile(user, {
      displayName: adminData.name
    });
    console.log('✅ Profile updated');

    // Create Firestore document
    const adminDoc = {
      id: user.uid,
      name: adminData.name,
      email: adminData.email,
      role: adminData.role,
      permissions: adminData.permissions,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLogin: new Date()
    };

    await setDoc(doc(db, 'admins', user.uid), adminDoc);
    console.log('✅ Admin document created');
    
    console.log('🎉 SUCCESS! Tamil admin created:');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔑 Password: tamiladmin');
    console.log('   🆔 UID:', user.uid);
    console.log('');
    console.log('🔐 You can now sign in with these credentials!');
    
    return { success: true, uid: user.uid };
    
  } catch (error) {
    console.error('❌ Error:', error);
    
    if (error.code === 'auth/email-already-in-use') {
      console.log('⚠️  Account already exists! Try signing in.');
    } else if (error.code === 'auth/weak-password') {
      console.log('⚠️  Password too weak (need 6+ characters)');
    } else {
      console.log('💡 Try the manual Firebase Console method instead');
    }
    
    return { success: false, error: error.message };
  }
})();

// Also make it available as a function
window.createTamilAdminQuick = async function() {
  // Same function content as above
  console.log('🚀 Creating Tamil Admin via Quick Method...');
  // ... (implementation would be the same)
};
