import React from "react";
import "./styles/touch-booking.css";
import "./styles/button-theme.css";
import "./styles/navbar-enhancements.css";
import "./styles/amenities-animation.css";
import { createTamilAdmin, checkTamilAdmin } from "./utils/createTamilAdmin";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import AdminLoginPage from "./pages/admin/AdminLoginPage";
import AdminDashboardPage from "./pages/admin/AdminDashboardPage";
import HotelManagementPage from "./pages/admin/HotelManagementPage";
import RoomManagementPage from "./pages/admin/RoomManagementPage";
import ProfilePage from "./pages/ProfilePage";
import BookingsPage from "./pages/BookingsPage";
import TouchBookingPage from "./pages/TouchBookingPage";
import AdminSetupPage from "./pages/AdminSetupPage";
import SignUpPage from "./pages/SignUpPage";
import SignInPage from "./pages/SignInPage";
import ForgotPasswordPage from "./pages/ForgotPasswordPage";
import ResetPasswordPage from "./pages/ResetPasswordPage";

const queryClient = new QueryClient();

const App = () => {
  // Make admin creation functions available in browser console
  React.useEffect(() => {
    (window as any).createTamilAdmin = createTamilAdmin;
    (window as any).checkTamilAdmin = checkTamilAdmin;

    console.log('🔧 Admin utilities loaded!');
    console.log('📋 Available commands:');
    console.log('   - createTamilAdmin() - Create Tamil admin account');
    console.log('   - checkTamilAdmin() - Check if Tamil admin exists');
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/signup" element={<SignUpPage />} />
            <Route path="/signin" element={<SignInPage />} />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />
            <Route path="/touch-booking" element={<TouchBookingPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/bookings" element={<BookingsPage />} />
            <Route path="/admin-setup" element={<AdminSetupPage />} />
            <Route path="/admin/login" element={<AdminLoginPage />} />
            <Route path="/admin/dashboard" element={<AdminDashboardPage />} />
            <Route path="/admin/hotels" element={<HotelManagementPage />} />
            <Route path="/admin/rooms" element={<RoomManagementPage />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
  );
};

export default App;
