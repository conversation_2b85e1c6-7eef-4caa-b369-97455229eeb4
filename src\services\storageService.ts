// Firebase Storage service for image uploads
import {
  ref,
  uploadBytes,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
  listAll
} from 'firebase/storage';
import { storage } from '@/lib/firebase';

// Storage paths
const HOTEL_IMAGES_PATH = 'hotels';
const ROOM_IMAGES_PATH = 'rooms';
const REVIEW_IMAGES_PATH = 'reviews';

export interface UploadProgress {
  progress: number;
  isComplete: boolean;
  downloadURL?: string;
  error?: string;
}

// Upload single image
export const uploadImage = async (
  file: File,
  path: string,
  fileName?: string
): Promise<string> => {
  try {
    const finalFileName = fileName || `${Date.now()}_${file.name}`;
    const imageRef = ref(storage, `${path}/${finalFileName}`);
    
    const snapshot = await uploadBytes(imageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

// Upload multiple images
export const uploadMultipleImages = async (
  files: File[],
  path: string
): Promise<string[]> => {
  try {
    const uploadPromises = files.map(file => 
      uploadImage(file, path, `${Date.now()}_${Math.random()}_${file.name}`)
    );
    
    const downloadURLs = await Promise.all(uploadPromises);
    return downloadURLs;
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
};

// Upload with progress tracking
export const uploadImageWithProgress = (
  file: File,
  path: string,
  fileName?: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const finalFileName = fileName || `${Date.now()}_${file.name}`;
    const imageRef = ref(storage, `${path}/${finalFileName}`);
    
    const uploadTask = uploadBytesResumable(imageRef, file);
    
    uploadTask.on(
      'state_changed',
      (snapshot) => {
        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        onProgress?.({
          progress,
          isComplete: false
        });
      },
      (error) => {
        console.error('Upload error:', error);
        onProgress?.({
          progress: 0,
          isComplete: false,
          error: error.message
        });
        reject(error);
      },
      async () => {
        try {
          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
          onProgress?.({
            progress: 100,
            isComplete: true,
            downloadURL
          });
          resolve(downloadURL);
        } catch (error) {
          reject(error);
        }
      }
    );
  });
};

// Delete image
export const deleteImage = async (imageURL: string): Promise<void> => {
  try {
    const imageRef = ref(storage, imageURL);
    await deleteObject(imageRef);
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
};

// Delete multiple images
export const deleteMultipleImages = async (imageURLs: string[]): Promise<void> => {
  try {
    const deletePromises = imageURLs.map(url => deleteImage(url));
    await Promise.all(deletePromises);
  } catch (error) {
    console.error('Error deleting multiple images:', error);
    throw error;
  }
};

// Hotel-specific upload functions
export const uploadHotelImage = async (
  file: File,
  hotelId: string,
  fileName?: string
): Promise<string> => {
  return uploadImage(file, `${HOTEL_IMAGES_PATH}/${hotelId}`, fileName);
};

export const uploadHotelImages = async (
  files: File[],
  hotelId: string
): Promise<string[]> => {
  return uploadMultipleImages(files, `${HOTEL_IMAGES_PATH}/${hotelId}`);
};

export const uploadHotelImageWithProgress = (
  file: File,
  hotelId: string,
  fileName?: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<string> => {
  return uploadImageWithProgress(file, `${HOTEL_IMAGES_PATH}/${hotelId}`, fileName, onProgress);
};

// Room-specific upload functions
export const uploadRoomImage = async (
  file: File,
  hotelId: string,
  roomId: string,
  fileName?: string
): Promise<string> => {
  return uploadImage(file, `${ROOM_IMAGES_PATH}/${hotelId}/${roomId}`, fileName);
};

export const uploadRoomImages = async (
  files: File[],
  hotelId: string,
  roomId: string
): Promise<string[]> => {
  return uploadMultipleImages(files, `${ROOM_IMAGES_PATH}/${hotelId}/${roomId}`);
};

export const uploadRoomImageWithProgress = (
  file: File,
  hotelId: string,
  roomId: string,
  fileName?: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<string> => {
  return uploadImageWithProgress(file, `${ROOM_IMAGES_PATH}/${hotelId}/${roomId}`, fileName, onProgress);
};

// Review-specific upload functions
export const uploadReviewImage = async (
  file: File,
  reviewId: string,
  fileName?: string
): Promise<string> => {
  return uploadImage(file, `${REVIEW_IMAGES_PATH}/${reviewId}`, fileName);
};

export const uploadReviewImages = async (
  files: File[],
  reviewId: string
): Promise<string[]> => {
  return uploadMultipleImages(files, `${REVIEW_IMAGES_PATH}/${reviewId}`);
};

// Get all images in a folder
export const getImagesInFolder = async (path: string): Promise<string[]> => {
  try {
    const folderRef = ref(storage, path);
    const result = await listAll(folderRef);
    
    const downloadURLs = await Promise.all(
      result.items.map(itemRef => getDownloadURL(itemRef))
    );
    
    return downloadURLs;
  } catch (error) {
    console.error('Error getting images in folder:', error);
    throw error;
  }
};

// Utility function to validate image file
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Invalid file type. Please upload JPEG, PNG, or WebP images.'
    };
  }
  
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File size too large. Please upload images smaller than 5MB.'
    };
  }
  
  return { isValid: true };
};

// Utility function to compress image before upload
export const compressImage = (file: File, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      const maxWidth = 1920;
      const maxHeight = 1080;
      
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }
      
      canvas.width = width;
      canvas.height = height;
      
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            resolve(file);
          }
        },
        file.type,
        quality
      );
    };
    
    img.src = URL.createObjectURL(file);
  });
};
