// Touch-friendly booking component with multiple room cards and scrolling
import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Calendar, 
  Users, 
  Phone, 
  Mail, 
  MapPin, 
  Star,
  Bed,
  Bath,
  Wifi,
  Car,
  Utensils,
  Wind,
  Tv,
  Coffee,
  Shield,
  Heart,
  Share2,
  ChevronLeft,
  ChevronRight,
  IndianRupee,
  Clock,
  CheckCircle,
  X,
  Hotel,
  Building,
  Sparkles,
  Briefcase,
  Crown
} from 'lucide-react';
import { toast } from 'sonner';

interface Room {
  id: string;
  name: string;
  type: string;
  price: number;
  originalPrice?: number;
  capacity: number;
  size: string;
  bedType: string;
  images: string[];
  amenities: string[];
  description: string;
  rating: number;
  reviews: number;
  available: number;
  total: number;
  features: string[];
  isPopular?: boolean;
  discount?: number;
}

interface TouchBookingProps {
  className?: string;
}

const TouchBooking: React.FC<TouchBookingProps> = ({ className }) => {
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [bookingStep, setBookingStep] = useState<'rooms' | 'details' | 'confirmation'>('rooms');
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [selectedFilter, setSelectedFilter] = useState<string>('all');

  // Sample room data with variety
  const rooms: Room[] = [
    {
      id: '1',
      name: 'Arunachala Heritage Suite',
      type: 'Heritage Suite',
      price: 8500,
      originalPrice: 10000,
      capacity: 4,
      size: '75 sqm',
      bedType: 'King + Sofa Bed',
      images: [
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png',
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png'
      ],
      amenities: ['Free WiFi', 'AC', 'Mini Bar', 'Balcony', 'Temple View', 'Room Service'],
      description: 'Luxurious heritage suite with traditional Tamil architecture and modern amenities',
      rating: 4.8,
      reviews: 124,
      available: 3,
      total: 5,
      features: ['Temple View', 'Heritage Architecture', 'Premium Amenities'],
      isPopular: true,
      discount: 15
    },
    {
      id: '2',
      name: 'Spiritual Retreat Room',
      type: 'Deluxe Room',
      price: 4200,
      capacity: 2,
      size: '45 sqm',
      bedType: 'Queen Size Bed',
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png'
      ],
      amenities: ['Free WiFi', 'AC', 'Meditation Corner', 'Garden View', 'Organic Toiletries'],
      description: 'Peaceful room designed for spiritual seekers with meditation facilities',
      rating: 4.6,
      reviews: 89,
      available: 8,
      total: 12,
      features: ['Meditation Space', 'Garden View', 'Organic Amenities']
    },
    {
      id: '3',
      name: 'Business Executive Room',
      type: 'Executive Room',
      price: 5500,
      capacity: 2,
      size: '50 sqm',
      bedType: 'King Size Bed',
      images: [
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png',
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png'
      ],
      amenities: ['Free WiFi', 'AC', 'Work Desk', 'Business Center Access', 'City View'],
      description: 'Modern executive room perfect for business travelers',
      rating: 4.4,
      reviews: 156,
      available: 6,
      total: 10,
      features: ['Business Facilities', 'City View', 'Work Space']
    },
    {
      id: '4',
      name: 'Family Comfort Suite',
      type: 'Family Suite',
      price: 7200,
      capacity: 6,
      size: '85 sqm',
      bedType: 'King + Twin Beds',
      images: [
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png'
      ],
      amenities: ['Free WiFi', 'AC', 'Kitchenette', 'Two Bathrooms', 'Kids Area'],
      description: 'Spacious family suite with separate areas for adults and children',
      rating: 4.7,
      reviews: 203,
      available: 4,
      total: 8,
      features: ['Family Friendly', 'Kitchenette', 'Extra Space']
    },
    {
      id: '5',
      name: 'Budget Comfort Room',
      type: 'Standard Room',
      price: 2800,
      capacity: 2,
      size: '30 sqm',
      bedType: 'Double Bed',
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png'
      ],
      amenities: ['Free WiFi', 'AC', 'TV', 'Hot Water', 'Daily Housekeeping'],
      description: 'Comfortable and affordable room with essential amenities',
      rating: 4.2,
      reviews: 312,
      available: 12,
      total: 15,
      features: ['Budget Friendly', 'Essential Amenities', 'Clean & Comfortable']
    },
    {
      id: '6',
      name: 'Honeymoon Villa',
      type: 'Villa',
      price: 12000,
      originalPrice: 15000,
      capacity: 2,
      size: '100 sqm',
      bedType: 'King Size Bed',
      images: [
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png'
      ],
      amenities: ['Private Pool', 'Jacuzzi', 'Butler Service', 'Champagne', 'Flower Decoration'],
      description: 'Romantic villa with private amenities perfect for couples',
      rating: 4.9,
      reviews: 67,
      available: 2,
      total: 3,
      features: ['Private Pool', 'Romantic Setting', 'Luxury Amenities'],
      discount: 20
    },
    {
      id: '7',
      name: 'Yoga Retreat Room',
      type: 'Wellness Room',
      price: 3800,
      capacity: 2,
      size: '40 sqm',
      bedType: 'Twin Beds',
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png'
      ],
      amenities: ['Yoga Mat', 'Meditation Cushions', 'Herbal Tea', 'Aromatherapy', 'Garden Access'],
      description: 'Wellness-focused room with yoga and meditation facilities',
      rating: 4.5,
      reviews: 145,
      available: 7,
      total: 10,
      features: ['Wellness Focus', 'Yoga Facilities', 'Natural Setting']
    },
    {
      id: '8',
      name: 'Temple View Deluxe',
      type: 'Deluxe Room',
      price: 4800,
      capacity: 3,
      size: '48 sqm',
      bedType: 'King + Single Bed',
      images: [
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png'
      ],
      amenities: ['Temple View', 'Prayer Corner', 'Religious Books', 'Incense', 'Spiritual Guidance'],
      description: 'Room with direct temple view and spiritual amenities',
      rating: 4.6,
      reviews: 198,
      available: 5,
      total: 8,
      features: ['Temple View', 'Spiritual Amenities', 'Cultural Experience']
    },
    {
      id: '9',
      name: 'Luxury Presidential Suite',
      type: 'Presidential Suite',
      price: 18000,
      originalPrice: 22000,
      capacity: 8,
      size: '150 sqm',
      bedType: 'King + Queen + Sofa Beds',
      images: [
        '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png'
      ],
      amenities: ['Private Terrace', 'Personal Chef', 'Concierge', 'Limousine Service', 'Premium Bar'],
      description: 'Ultimate luxury suite with personalized services and premium amenities',
      rating: 5.0,
      reviews: 34,
      available: 1,
      total: 2,
      features: ['Ultimate Luxury', 'Personal Services', 'Exclusive Amenities'],
      isPopular: true,
      discount: 18
    },
    {
      id: '10',
      name: 'Garden Cottage',
      type: 'Cottage',
      price: 6200,
      capacity: 4,
      size: '60 sqm',
      bedType: 'Queen + Twin Beds',
      images: [
        '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png',
        '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png'
      ],
      amenities: ['Private Garden', 'Outdoor Seating', 'BBQ Facility', 'Nature View', 'Bird Watching'],
      description: 'Charming cottage surrounded by lush gardens and nature',
      rating: 4.7,
      reviews: 178,
      available: 6,
      total: 8,
      features: ['Private Garden', 'Nature Setting', 'Outdoor Activities']
    }
  ];

  const [bookingData, setBookingData] = useState({
    checkIn: '',
    checkOut: '',
    guests: '2',
    name: '',
    email: '',
    phone: '',
    specialRequests: ''
  });

  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi')) return <Wifi className="w-4 h-4" />;
    if (amenityLower.includes('ac') || amenityLower.includes('air')) return <Wind className="w-4 h-4" />;
    if (amenityLower.includes('tv')) return <Tv className="w-4 h-4" />;
    if (amenityLower.includes('bath')) return <Bath className="w-4 h-4" />;
    if (amenityLower.includes('car') || amenityLower.includes('parking')) return <Car className="w-4 h-4" />;
    if (amenityLower.includes('restaurant') || amenityLower.includes('dining')) return <Utensils className="w-4 h-4" />;
    if (amenityLower.includes('coffee') || amenityLower.includes('tea')) return <Coffee className="w-4 h-4" />;
    if (amenityLower.includes('security') || amenityLower.includes('safe')) return <Shield className="w-4 h-4" />;
    return <CheckCircle className="w-4 h-4" />;
  };

  // Touch handlers for image swiping with improved functionality
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = (room: Room, roomIndex: number) => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    // Create unique image index for each room
    const imageKey = `room-${roomIndex}`;
    const currentIndex = currentImageIndex || 0;

    if (isLeftSwipe && currentIndex < room.images.length - 1) {
      setCurrentImageIndex(currentIndex + 1);
    }
    if (isRightSwipe && currentIndex > 0) {
      setCurrentImageIndex(currentIndex - 1);
    }
  };

  // Enhanced image navigation
  const nextImage = (room: Room, roomIndex: number) => {
    const currentIndex = currentImageIndex || 0;
    if (currentIndex < room.images.length - 1) {
      setCurrentImageIndex(currentIndex + 1);
    } else {
      setCurrentImageIndex(0); // Loop back to first image
    }
  };

  const prevImage = (room: Room, roomIndex: number) => {
    const currentIndex = currentImageIndex || 0;
    if (currentIndex > 0) {
      setCurrentImageIndex(currentIndex - 1);
    } else {
      setCurrentImageIndex(room.images.length - 1); // Loop to last image
    }
  };

  const toggleFavorite = (roomId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(roomId)) {
        newFavorites.delete(roomId);
        toast.success('Removed from favorites');
      } else {
        newFavorites.add(roomId);
        toast.success('Added to favorites');
      }
      return newFavorites;
    });
  };

  const handleBookRoom = (room: Room) => {
    setSelectedRoom(room);
    setBookingStep('details');
  };

  const handleInputChange = (field: string, value: string) => {
    setBookingData(prev => ({ ...prev, [field]: value }));
  };

  const handleBookingSubmit = () => {
    if (!selectedRoom) return;

    // Validate required fields
    if (!bookingData.checkIn || !bookingData.checkOut || !bookingData.name || !bookingData.email || !bookingData.phone) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Simulate booking submission
    toast.success('Booking submitted successfully!');
    setBookingStep('confirmation');
  };

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: 'smooth' });
    }
  };

  if (bookingStep === 'details' && selectedRoom) {
    return (
      <section className={cn('py-16 md:py-24 bg-gradient-to-br from-blue-50 to-indigo-100', className)}>
        <div className="container mx-auto px-4 md:px-6">
          <FadeIn>
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center gap-4 mb-8">
                <Button
                  variant="outline"
                  onClick={() => setBookingStep('rooms')}
                  className="flex items-center gap-2"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Back to Rooms
                </Button>
                <div>
                  <h2 className="text-2xl font-bold">Complete Your Booking</h2>
                  <p className="text-muted-foreground">{selectedRoom.name}</p>
                </div>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                {/* Room Summary */}
                <Card>
                  <CardContent className="p-6">
                    <div className="relative mb-4">
                      <img
                        src={selectedRoom.images[0]}
                        alt={selectedRoom.name}
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      {selectedRoom.discount && (
                        <Badge className="absolute top-4 right-4 bg-red-500">
                          {selectedRoom.discount}% OFF
                        </Badge>
                      )}
                    </div>

                    <h3 className="text-xl font-semibold mb-2">{selectedRoom.name}</h3>
                    <div className="flex items-center gap-2 mb-4">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{selectedRoom.rating}</span>
                      <span className="text-muted-foreground">({selectedRoom.reviews} reviews)</span>
                    </div>

                    <div className="space-y-2 text-sm mb-4">
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-muted-foreground" />
                        <span>Up to {selectedRoom.capacity} guests</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Bed className="w-4 h-4 text-muted-foreground" />
                        <span>{selectedRoom.bedType}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <span>{selectedRoom.size}</span>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-semibold">Total Amount:</span>
                        <div className="text-right">
                          {selectedRoom.originalPrice && (
                            <div className="text-sm text-muted-foreground line-through">
                              ₹{selectedRoom.originalPrice.toLocaleString()}
                            </div>
                          )}
                          <div className="text-2xl font-bold text-primary">
                            ₹{selectedRoom.price.toLocaleString()}
                          </div>
                          <div className="text-sm text-muted-foreground">per night</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Booking Form */}
                <Card>
                  <CardHeader>
                    <CardTitle>Guest Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="checkin">Check-in Date</Label>
                        <Input
                          id="checkin"
                          type="date"
                          value={bookingData.checkIn}
                          onChange={(e) => handleInputChange('checkIn', e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="checkout">Check-out Date</Label>
                        <Input
                          id="checkout"
                          type="date"
                          value={bookingData.checkOut}
                          onChange={(e) => handleInputChange('checkOut', e.target.value)}
                          min={bookingData.checkIn || new Date().toISOString().split('T')[0]}
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="guests">Number of Guests</Label>
                      <Select value={bookingData.guests} onValueChange={(value) => handleInputChange('guests', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: selectedRoom.capacity }, (_, i) => (
                            <SelectItem key={i + 1} value={(i + 1).toString()}>
                              {i + 1} Guest{i > 0 ? 's' : ''}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="name">Full Name *</Label>
                      <Input
                        id="name"
                        value={bookingData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter your full name"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={bookingData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone Number *</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={bookingData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="+91 9876543210"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="requests">Special Requests (Optional)</Label>
                      <Input
                        id="requests"
                        value={bookingData.specialRequests}
                        onChange={(e) => handleInputChange('specialRequests', e.target.value)}
                        placeholder="Any special requirements..."
                      />
                    </div>

                    <Button
                      onClick={handleBookingSubmit}
                      className="w-full"
                      size="lg"
                    >
                      Confirm Booking
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </FadeIn>
        </div>
      </section>
    );
  }

  if (bookingStep === 'confirmation') {
    return (
      <section className={cn('py-16 md:py-24 bg-gradient-to-br from-green-50 to-emerald-100', className)}>
        <div className="container mx-auto px-4 md:px-6">
          <FadeIn>
            <div className="max-w-2xl mx-auto text-center">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-10 h-10 text-green-600" />
              </div>
              <h2 className="text-3xl font-bold mb-4">Booking Confirmed!</h2>
              <p className="text-lg text-muted-foreground mb-8">
                Thank you for choosing {selectedRoom?.name}. Your booking has been confirmed and you will receive a confirmation email shortly.
              </p>
              <div className="flex gap-4 justify-center">
                <Button onClick={() => setBookingStep('rooms')}>
                  Book Another Room
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/'}>
                  Back to Home
                </Button>
              </div>
            </div>
          </FadeIn>
        </div>
      </section>
    );
  }

  // Main room selection interface
  return (
    <section className={cn('py-16 md:py-24 bg-gradient-to-br from-orange-50 to-red-50', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Choose Your Perfect Room
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover our variety of rooms designed for every type of traveler. Swipe through our collection and find your ideal stay.
            </p>
          </div>
        </FadeIn>

        {/* Enhanced Room Categories Filter */}
        <FadeIn delay={100}>
          <div className="flex flex-wrap justify-center gap-3 mb-8">
            {[
              { name: 'All Rooms', icon: Hotel, filter: 'all' },
              { name: 'Heritage', icon: Building, filter: 'heritage' },
              { name: 'Deluxe', icon: Sparkles, filter: 'deluxe' },
              { name: 'Family', icon: Users, filter: 'family' },
              { name: 'Business', icon: Briefcase, filter: 'business' },
              { name: 'Wellness', icon: Heart, filter: 'wellness' },
              { name: 'Luxury', icon: Crown, filter: 'luxury' }
            ].map((category) => (
              <Button
                key={category.filter}
                variant={selectedFilter === category.filter ? "default" : "outline"}
                size="sm"
                className={cn(
                  "rounded-full px-4 py-2 transition-all duration-300 hover:scale-105 hover:shadow-lg",
                  selectedFilter === category.filter
                    ? "bg-gradient-to-r from-primary to-primary/80 text-white shadow-lg"
                    : "bg-white/80 backdrop-blur-sm hover:bg-white"
                )}
                onClick={() => setSelectedFilter(category.filter)}
              >
                <category.icon className="w-4 h-4 mr-2" />
                {category.name}
              </Button>
            ))}
          </div>
        </FadeIn>

        {/* Scrollable Room Cards */}
        <FadeIn delay={200}>
          <div className="relative">
            {/* Scroll Navigation Buttons */}
            <Button
              variant="outline"
              size="icon"
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm shadow-lg"
              onClick={scrollLeft}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm shadow-lg"
              onClick={scrollRight}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>

            {/* Room Cards Container */}
            <div
              ref={scrollContainerRef}
              className="flex gap-6 overflow-x-auto scrollbar-hide pb-4 px-12"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              {rooms
                .filter(room => {
                  if (selectedFilter === 'all') return true;
                  return room.type.toLowerCase().includes(selectedFilter.toLowerCase());
                })
                .map((room, index) => (
                <FadeIn key={room.id} delay={300 + index * 50}>
                  <Card className="room-card flex-shrink-0 w-80 overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:scale-105 bg-white/90 backdrop-blur-sm border-0 shadow-lg">
                    {/* Room Image with Enhanced Touch Swipe */}
                    <div
                      className="relative h-56 overflow-hidden cursor-pointer group"
                      onTouchStart={handleTouchStart}
                      onTouchMove={handleTouchMove}
                      onTouchEnd={() => handleTouchEnd(room, index)}
                    >
                      <img
                        src={room.images[currentImageIndex] || room.images[0]}
                        alt={room.name}
                        className="w-full h-full object-cover transition-all duration-500 group-hover:scale-110"
                      />

                      {/* Image Navigation Arrows */}
                      {room.images.length > 1 && (
                        <>
                          <button
                            className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 z-10"
                            onClick={(e) => {
                              e.stopPropagation();
                              prevImage(room, index);
                            }}
                          >
                            <ChevronLeft className="w-4 h-4" />
                          </button>
                          <button
                            className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 z-10"
                            onClick={(e) => {
                              e.stopPropagation();
                              nextImage(room, index);
                            }}
                          >
                            <ChevronRight className="w-4 h-4" />
                          </button>
                        </>
                      )}

                      {/* Image Overlay with Badges */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

                      {/* Top Badges */}
                      <div className="absolute top-4 left-4 flex gap-2">
                        {room.isPopular && (
                          <Badge className="bg-orange-500 text-white flex items-center gap-1">
                            <Star className="w-3 h-3 fill-white" />
                            Popular
                          </Badge>
                        )}
                        {room.discount && (
                          <Badge className="bg-red-500 text-white">
                            {room.discount}% OFF
                          </Badge>
                        )}
                      </div>

                      {/* Favorite Button */}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm hover:bg-white/30"
                        onClick={() => toggleFavorite(room.id)}
                      >
                        <Heart
                          className={cn(
                            "w-5 h-5",
                            favorites.has(room.id) ? "fill-red-500 text-red-500" : "text-white"
                          )}
                        />
                      </Button>

                      {/* Image Navigation Dots */}
                      {room.images.length > 1 && (
                        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-1">
                          {room.images.map((_, imgIndex) => (
                            <div
                              key={imgIndex}
                              className={cn(
                                "w-2 h-2 rounded-full transition-all",
                                imgIndex === currentImageIndex ? "bg-white" : "bg-white/50"
                              )}
                            />
                          ))}
                        </div>
                      )}

                      {/* Price Badge */}
                      <div className="absolute bottom-4 right-4">
                        <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                          {room.originalPrice && (
                            <div className="text-xs text-muted-foreground line-through">
                              ₹{room.originalPrice.toLocaleString()}
                            </div>
                          )}
                          <div className="text-lg font-bold text-primary">
                            ₹{room.price.toLocaleString()}
                          </div>
                          <div className="text-xs text-muted-foreground">per night</div>
                        </div>
                      </div>
                    </div>

                    {/* Room Details */}
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Room Header */}
                        <div>
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h3 className="text-xl font-semibold line-clamp-1">{room.name}</h3>
                              <Badge variant="outline" className="mt-1 capitalize">
                                {room.type}
                              </Badge>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="flex-shrink-0"
                            >
                              <Share2 className="w-4 h-4" />
                            </Button>
                          </div>

                          <div className="flex items-center gap-2 mb-3">
                            <div className="flex items-center gap-1">
                              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                              <span className="font-medium text-sm">{room.rating}</span>
                            </div>
                            <span className="text-sm text-muted-foreground">
                              ({room.reviews} reviews)
                            </span>
                          </div>
                        </div>

                        {/* Room Info */}
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-muted-foreground" />
                            <span>{room.capacity} guests</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <MapPin className="w-4 h-4 text-muted-foreground" />
                            <span>{room.size}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Bed className="w-4 h-4 text-muted-foreground" />
                            <span>{room.bedType}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4 text-muted-foreground" />
                            <span>{room.available}/{room.total} available</span>
                          </div>
                        </div>

                        {/* Room Description */}
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {room.description}
                        </p>

                        {/* Features */}
                        <div className="flex flex-wrap gap-1">
                          {room.features.slice(0, 3).map((feature, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                          {room.features.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{room.features.length - 3} more
                            </Badge>
                          )}
                        </div>

                        {/* Amenities */}
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-sm font-medium">Amenities:</span>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {room.amenities.slice(0, 4).map((amenity, idx) => (
                              <div key={idx} className="flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-full">
                                {getAmenityIcon(amenity)}
                                <span>{amenity}</span>
                              </div>
                            ))}
                            {room.amenities.length > 4 && (
                              <div className="flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-full">
                                <span>+{room.amenities.length - 4} more</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Availability Status */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {room.available > 0 ? (
                              <div className="flex items-center gap-1 text-green-600">
                                <CheckCircle className="w-4 h-4" />
                                <span className="text-sm font-medium">Available</span>
                              </div>
                            ) : (
                              <div className="flex items-center gap-1 text-red-600">
                                <X className="w-4 h-4" />
                                <span className="text-sm font-medium">Sold Out</span>
                              </div>
                            )}
                          </div>

                          {room.available <= 3 && room.available > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              Only {room.available} left!
                            </Badge>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 pt-2">
                          <Button
                            className="flex-1"
                            onClick={() => handleBookRoom(room)}
                            disabled={room.available === 0}
                          >
                            {room.available === 0 ? 'Sold Out' : 'Book Now'}
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => toast.info('Room details feature coming soon!')}
                          >
                            <MapPin className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </FadeIn>
              ))}
            </div>
          </div>
        </FadeIn>

        {/* Bottom CTA */}
        <FadeIn delay={400}>
          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              Need help choosing the perfect room?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline">
                <Phone className="w-4 h-4 mr-2" />
                Call Us: +91 4175 234567
              </Button>
              <Button variant="outline">
                <Mail className="w-4 h-4 mr-2" />
                Email: <EMAIL>
              </Button>
            </div>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default TouchBooking;
