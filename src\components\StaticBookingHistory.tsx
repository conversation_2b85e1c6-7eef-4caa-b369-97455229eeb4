// Booking history component that fetches real data from Firebase
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  MapPin, 
  Users, 
  IndianRupee, 
  Phone,
  Mail,
  FileText,
  User,
  Loader2,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import { getUserBookings } from '@/services/bookingService';
import { Booking, BookingStatus } from '@/types/booking';
import { formatCurrency, formatDate } from '@/lib/utils';

interface StaticBookingHistoryProps {
  className?: string;
}

const StaticBookingHistory: React.FC<StaticBookingHistoryProps> = ({ className }) => {
  const { user, firebaseUser } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Test Firestore connection
  const testFirestoreConnection = async () => {
    try {
      console.log('Testing Firestore connection...');
      const { collection, getDocs } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase');

      // Try to read from a simple collection
      const testQuery = collection(db, 'bookings');
      const snapshot = await getDocs(testQuery);
      console.log('Firestore connection test successful. Documents found:', snapshot.size);
      return true;
    } catch (error) {
      console.error('Firestore connection test failed:', error);
      return false;
    }
  };

  // Fetch real bookings from Firestore
  const fetchBookings = async () => {
    if (!firebaseUser) {
      setError('Please sign in to view your bookings');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('=== BOOKING FETCH DEBUG ===');
      console.log('User UID:', firebaseUser.uid);
      console.log('User email:', firebaseUser.email);
      console.log('User display name:', firebaseUser.displayName);

      // Test Firestore connection first
      const connectionOk = await testFirestoreConnection();
      if (!connectionOk) {
        throw new Error('Cannot connect to Firestore database');
      }

      console.log('Calling getUserBookings...');
      const result = await getUserBookings(firebaseUser.uid, 20);

      console.log('getUserBookings result:', result);
      console.log('Number of bookings found:', result.bookings.length);

      setBookings(result.bookings);

      if (result.bookings.length === 0) {
        console.log('No bookings found for user');
        setError('No bookings found. Try making a booking first!');
      } else {
        console.log('Successfully loaded', result.bookings.length, 'bookings');
      }
    } catch (error: any) {
      console.error('=== BOOKING FETCH ERROR ===');
      console.error('Error details:', error);
      console.error('Error message:', error.message);
      console.error('Error code:', error.code);

      let errorMessage = 'Failed to load bookings';
      if (error.message) {
        errorMessage += ': ' + error.message;
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      console.log('=== BOOKING FETCH COMPLETE ===');
    }
  };

  useEffect(() => {
    fetchBookings();
  }, [firebaseUser]);

  // Create a test booking for debugging
  const createTestBooking = async () => {
    if (!firebaseUser) {
      toast.error('Please sign in first');
      return;
    }

    try {
      console.log('=== CREATING TEST BOOKING ===');
      console.log('User:', firebaseUser.uid, firebaseUser.email);

      // Import Firebase functions
      const { collection, addDoc, Timestamp } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase');

      const testBooking = {
        // Hotel information
        hotelId: 'test-hotel-' + Date.now(),
        hotelName: 'Arunachala Test Hotel',

        // Guest information
        guestName: firebaseUser.displayName || 'Test User',
        guestEmail: firebaseUser.email || '<EMAIL>',
        guestPhone: '+91 9876543210',

        // Booking dates
        checkIn: Timestamp.fromDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // Tomorrow
        checkOut: Timestamp.fromDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)), // 3 days from now

        // Room details
        rooms: [{
          roomId: 'test-room-1',
          roomType: 'Deluxe Room',
          quantity: 1,
          pricePerNight: 2500,
          amenities: ['WiFi', 'AC', 'TV'],
          maxOccupancy: 2
        }],

        // Guest details
        adults: 2,
        children: 0,
        nights: 2,
        totalRooms: 1,
        totalAmount: 5000,

        // Booking metadata
        bookingSource: 'website',
        userId: firebaseUser.uid,
        createdBy: firebaseUser.uid,
        guestNotes: 'Test booking created for debugging - ' + new Date().toLocaleString(),

        // Timestamps
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),

        // Status
        status: 'confirmed',
        paymentStatus: 'pending'
      };

      console.log('Test booking data:', testBooking);

      // Add document directly to Firestore
      const docRef = await addDoc(collection(db, 'bookings'), testBooking);
      console.log('Test booking created with ID:', docRef.id);

      toast.success(`Test booking created successfully! ID: ${docRef.id}`);

      // Refresh bookings to show the new one
      setTimeout(() => {
        console.log('Refreshing bookings after test booking creation...');
        fetchBookings();
      }, 1500);

    } catch (error: any) {
      console.error('=== TEST BOOKING CREATION ERROR ===');
      console.error('Error creating test booking:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);

      toast.error('Failed to create test booking: ' + error.message);
    }
  };

  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case BookingStatus.CONFIRMED:
        return 'bg-green-500';
      case BookingStatus.PENDING:
        return 'bg-yellow-500';
      case BookingStatus.CANCELLED:
        return 'bg-red-500';
      case BookingStatus.CHECKED_IN:
        return 'bg-blue-500';
      case BookingStatus.CHECKED_OUT:
        return 'bg-gray-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: BookingStatus) => {
    switch (status) {
      case BookingStatus.CONFIRMED:
        return 'Confirmed';
      case BookingStatus.PENDING:
        return 'Pending';
      case BookingStatus.CANCELLED:
        return 'Cancelled';
      case BookingStatus.CHECKED_IN:
        return 'Checked In';
      case BookingStatus.CHECKED_OUT:
        return 'Checked Out';
      default:
        return status;
    }
  };

  if (!firebaseUser) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-12">
          <Calendar className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-xl font-semibold mb-2">Sign In Required</h3>
          <p className="text-muted-foreground">
            Please sign in to view your booking history.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold mb-2">My Bookings</h2>
          <p className="text-muted-foreground">
            View and manage your hotel reservations
          </p>
        </div>
        {!loading && firebaseUser && (
          <div className="flex gap-2">
            <Button onClick={fetchBookings} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={createTestBooking} variant="outline" size="sm">
              Create Test Booking
            </Button>
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="text-center py-12">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading your bookings...</p>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && !loading && (
        <Card>
          <CardContent className="text-center py-12">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Bookings</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchBookings} className="btn-primary">
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!loading && !error && bookings.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Calendar className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-xl font-semibold mb-2">No Bookings Yet</h3>
            <p className="text-muted-foreground mb-4">
              You haven't made any bookings yet. Start exploring our hotels!
            </p>
            <Button onClick={() => window.location.href = '/#booking'}>
              Book Your Stay
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Bookings List */}
      {!loading && !error && bookings.length > 0 && (
        <div className="space-y-4">
          {bookings.map((booking) => (
            <Card key={booking.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-xl font-semibold mb-1">
                      {booking.hotelName}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      {booking.rooms?.[0]?.roomType || 'Room'} • {booking.totalRooms} room{booking.totalRooms > 1 ? 's' : ''}
                    </p>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <MapPin className="w-4 h-4" />
                      <span>Booking ID: {booking.id}</span>
                    </div>
                  </div>
                  <Badge className={`${getStatusColor(booking.status)} text-white`}>
                    {getStatusText(booking.status)}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">Check-in</div>
                      <div className="text-muted-foreground">
                        {formatDate(booking.checkIn.toDate())}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">Check-out</div>
                      <div className="text-muted-foreground">
                        {formatDate(booking.checkOut.toDate())}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">Guests</div>
                      <div className="text-muted-foreground">
                        {booking.adults} adult{booking.adults !== 1 ? 's' : ''}{booking.children > 0 && `, ${booking.children} child${booking.children !== 1 ? 'ren' : ''}`}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <IndianRupee className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">Total</div>
                      <div className="text-lg font-bold text-primary">
                        {formatCurrency(booking.totalAmount)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Guest Information */}
                <div className="bg-muted/50 rounded-lg p-3 mb-4">
                  <h4 className="font-medium mb-2 text-sm">Guest Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-muted-foreground" />
                      <span>{booking.guestName}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-muted-foreground" />
                      <span>{booking.guestEmail}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      <span>{booking.guestPhone}</span>
                    </div>
                  </div>
                </div>

                {/* Special Requests */}
                {booking.guestNotes && (
                  <div className="mb-4">
                    <div className="flex items-center gap-2 text-sm">
                      <FileText className="w-4 h-4 text-muted-foreground" />
                      <span className="font-medium">Special Requests:</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1 pl-6">
                      {booking.guestNotes}
                    </p>
                  </div>
                )}

                {/* Booking Date */}
                <div className="pt-4 border-t">
                  <p className="text-xs text-muted-foreground">
                    Booked on: {formatDate(booking.createdAt.toDate())}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default StaticBookingHistory;
