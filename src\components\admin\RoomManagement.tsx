// Room management component for admin
import React, { useState, useEffect } from 'react';
import { getAllHotels, getHotelRooms, createRoom, updateRoom } from '@/services/hotelService';
import { uploadRoomImages } from '@/services/storageService';
import { Hotel, Room } from '@/types/hotel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Plus, Edit, Users, IndianRupee, Bed, X } from 'lucide-react';
import { toast } from 'sonner';

const RoomManagement: React.FC = () => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);
  const [formData, setFormData] = useState({
    hotelId: '',
    name: '',
    description: '',
    type: 'deluxe' as Room['type'],
    capacity: 2,
    size: '',
    price: 0,
    amenities: '',
    bedType: '',
    totalRooms: 1,
    availableRooms: 1
  });
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);

  const loadHotels = async () => {
    try {
      const hotelsData = await getAllHotels();
      setHotels(hotelsData);
      if (hotelsData.length > 0 && !selectedHotel) {
        setSelectedHotel(hotelsData[0].id);
      }
    } catch (error) {
      console.error('Error loading hotels:', error);
      toast.error('Failed to load hotels');
    }
  };

  const loadRooms = async (hotelId: string) => {
    if (!hotelId) return;
    
    try {
      setLoading(true);
      const roomsData = await getHotelRooms(hotelId);
      setRooms(roomsData);
    } catch (error) {
      console.error('Error loading rooms:', error);
      toast.error('Failed to load rooms');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadHotels();
  }, []);

  useEffect(() => {
    if (selectedHotel) {
      loadRooms(selectedHotel);
    }
  }, [selectedHotel]);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setSelectedImages(files);
  };

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  const resetForm = () => {
    setFormData({
      hotelId: selectedHotel,
      name: '',
      description: '',
      type: 'deluxe',
      capacity: 2,
      size: '',
      price: 0,
      amenities: '',
      bedType: '',
      totalRooms: 1,
      availableRooms: 1
    });
    setSelectedImages([]);
    setEditingRoom(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.hotelId) {
      toast.error('Please select a hotel');
      return;
    }

    setUploading(true);

    try {
      let imageUrls: string[] = [];
      
      if (selectedImages.length > 0) {
        const tempRoomId = editingRoom?.id || `temp_${Date.now()}`;
        imageUrls = await uploadRoomImages(selectedImages, formData.hotelId, tempRoomId);
      }

      const roomData = {
        ...formData,
        amenities: formData.amenities.split(',').map(a => a.trim()).filter(a => a),
        images: editingRoom ? [...(editingRoom.images || []), ...imageUrls] : imageUrls,
        isActive: true
      };

      if (editingRoom) {
        await updateRoom(editingRoom.id, roomData);
        toast.success('Room updated successfully!');
      } else {
        await createRoom(roomData);
        toast.success('Room created successfully!');
      }

      setDialogOpen(false);
      resetForm();
      loadRooms(selectedHotel);
    } catch (error) {
      console.error('Error saving room:', error);
      toast.error('Failed to save room');
    } finally {
      setUploading(false);
    }
  };

  const handleEdit = (room: Room) => {
    setEditingRoom(room);
    setFormData({
      hotelId: room.hotelId,
      name: room.name,
      description: room.description,
      type: room.type,
      capacity: room.capacity,
      size: room.size,
      price: room.price,
      amenities: room.amenities.join(', '),
      bedType: room.bedType,
      totalRooms: room.totalRooms,
      availableRooms: room.availableRooms
    });
    setDialogOpen(true);
  };

  const selectedHotelData = hotels.find(h => h.id === selectedHotel);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Room Management</h1>
          <p className="text-muted-foreground mt-2">
            Manage rooms for your hotels
          </p>
        </div>
        
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm} disabled={!selectedHotel}>
              <Plus className="w-4 h-4 mr-2" />
              Add Room
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingRoom ? 'Edit Room' : 'Add New Room'}
              </DialogTitle>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="hotelId">Hotel *</Label>
                <Select
                  value={formData.hotelId}
                  onValueChange={(value) => handleInputChange('hotelId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select hotel" />
                  </SelectTrigger>
                  <SelectContent>
                    {hotels.map((hotel) => (
                      <SelectItem key={hotel.id} value={hotel.id}>
                        {hotel.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Room Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="type">Room Type *</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => handleInputChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="single">Single</SelectItem>
                      <SelectItem value="double">Double</SelectItem>
                      <SelectItem value="deluxe">Deluxe</SelectItem>
                      <SelectItem value="suite">Suite</SelectItem>
                      <SelectItem value="family">Family</SelectItem>
                      <SelectItem value="executive">Executive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="capacity">Capacity *</Label>
                  <Input
                    id="capacity"
                    type="number"
                    min="1"
                    value={formData.capacity}
                    onChange={(e) => handleInputChange('capacity', parseInt(e.target.value))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="size">Size</Label>
                  <Input
                    id="size"
                    value={formData.size}
                    onChange={(e) => handleInputChange('size', e.target.value)}
                    placeholder="e.g., 35 sqm"
                  />
                </div>
                <div>
                  <Label htmlFor="price">Price per Night (₹) *</Label>
                  <Input
                    id="price"
                    type="number"
                    min="0"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', parseInt(e.target.value))}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="bedType">Bed Type</Label>
                  <Input
                    id="bedType"
                    value={formData.bedType}
                    onChange={(e) => handleInputChange('bedType', e.target.value)}
                    placeholder="e.g., King Size Bed"
                  />
                </div>
                <div>
                  <Label htmlFor="totalRooms">Total Rooms *</Label>
                  <Input
                    id="totalRooms"
                    type="number"
                    min="1"
                    value={formData.totalRooms}
                    onChange={(e) => {
                      const total = parseInt(e.target.value);
                      handleInputChange('totalRooms', total);
                      handleInputChange('availableRooms', total);
                    }}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="amenities">Amenities (comma-separated)</Label>
                <Input
                  id="amenities"
                  value={formData.amenities}
                  onChange={(e) => handleInputChange('amenities', e.target.value)}
                  placeholder="Free WiFi, AC, TV, Mini Fridge"
                />
              </div>

              <div>
                <Label htmlFor="images">Room Images</Label>
                <Input
                  id="images"
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageSelect}
                  className="mt-1"
                />
                {selectedImages.length > 0 && (
                  <div className="mt-2 space-y-2">
                    {selectedImages.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                        <span className="text-sm">{file.name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeImage(index)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={uploading}>
                  {uploading ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      {editingRoom ? 'Updating...' : 'Creating...'}
                    </div>
                  ) : (
                    editingRoom ? 'Update Room' : 'Create Room'
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Hotel Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Hotel</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Select value={selectedHotel} onValueChange={setSelectedHotel}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Select a hotel" />
              </SelectTrigger>
              <SelectContent>
                {hotels.map((hotel) => (
                  <SelectItem key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedHotelData && (
              <div className="text-sm text-muted-foreground">
                {selectedHotelData.address}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Rooms List */}
      {loading ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      ) : (
        <div className="grid gap-6">
          {rooms.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <div className="text-muted-foreground">
                  <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
                    <Bed className="w-8 h-8" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No rooms found</h3>
                  <p className="text-sm mb-4">
                    {selectedHotel ? 'Add rooms to this hotel to get started.' : 'Select a hotel to view its rooms.'}
                  </p>
                  {selectedHotel && (
                    <Button onClick={() => setDialogOpen(true)}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Your First Room
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {rooms.map((room) => (
                <Card key={room.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-lg font-semibold">{room.name}</h3>
                          <Badge variant="outline" className="capitalize">
                            {room.type}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground text-sm mb-3">{room.description}</p>

                        <div className="space-y-2 text-sm">
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-muted-foreground" />
                            <span>Capacity: {room.capacity} guests</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <IndianRupee className="w-4 h-4 text-muted-foreground" />
                            <span>₹{room.price.toLocaleString()}/night</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Bed className="w-4 h-4 text-muted-foreground" />
                            <span>{room.bedType || 'Standard Bed'}</span>
                          </div>
                          {room.size && (
                            <div className="text-muted-foreground">
                              Size: {room.size}
                            </div>
                          )}
                        </div>

                        <div className="mt-3">
                          <div className="flex items-center justify-between text-sm">
                            <span>Availability:</span>
                            <span className="font-medium">
                              {room.availableRooms}/{room.totalRooms} available
                            </span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2 mt-1">
                            <div
                              className="bg-primary h-2 rounded-full"
                              style={{
                                width: `${(room.availableRooms / room.totalRooms) * 100}%`
                              }}
                            />
                          </div>
                        </div>

                        {room.amenities.length > 0 && (
                          <div className="mt-4">
                            <div className="flex flex-wrap gap-1">
                              {room.amenities.slice(0, 3).map((amenity, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {amenity}
                                </Badge>
                              ))}
                              {room.amenities.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{room.amenities.length - 3}
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(room)}
                        className="ml-2"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>

                    {room.images && room.images.length > 0 && (
                      <div className="mt-4">
                        <div className="flex gap-2 overflow-x-auto">
                          {room.images.slice(0, 3).map((image, index) => (
                            <img
                              key={index}
                              src={image}
                              alt={`${room.name} ${index + 1}`}
                              className="w-16 h-16 object-cover rounded border flex-shrink-0"
                            />
                          ))}
                          {room.images.length > 3 && (
                            <div className="w-16 h-16 bg-muted rounded border flex items-center justify-center text-xs text-muted-foreground flex-shrink-0">
                              +{room.images.length - 3}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RoomManagement;
