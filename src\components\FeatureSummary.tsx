// Feature summary component showing what's been implemented
import React from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  Smartphone, 
  Hotel, 
  Calendar, 
  Users, 
  Star,
  Heart,
  Share2,
  Filter,
  Search,
  Bell,
  Shield,
  Zap,
  Globe
} from 'lucide-react';

interface FeatureSummaryProps {
  className?: string;
}

const FeatureSummary: React.FC<FeatureSummaryProps> = ({ className }) => {
  const features = [
    {
      category: "Room Management",
      icon: <Hotel className="w-6 h-6" />,
      items: [
        "10 Different Room Types",
        "Static Room Data (No Firebase Dependency)",
        "Room Categories & Filtering",
        "Availability Status",
        "Price Comparison",
        "Room Amenities Display"
      ]
    },
    {
      category: "Touch-Friendly Interface",
      icon: <Smartphone className="w-6 h-6" />,
      items: [
        "Swipe Navigation for Images",
        "Touch-Optimized Cards",
        "Horizontal Scrolling",
        "Mobile-Responsive Design",
        "Touch Feedback",
        "Gesture Support"
      ]
    },
    {
      category: "Booking System",
      icon: <Calendar className="w-6 h-6" />,
      items: [
        "Simple Booking Form",
        "Date Validation",
        "Guest Information Collection",
        "Special Requests",
        "Booking Confirmation",
        "Email Notifications"
      ]
    },
    {
      category: "User Experience",
      icon: <Users className="w-6 h-6" />,
      items: [
        "User Authentication",
        "Booking History",
        "User Profiles",
        "Favorites System",
        "Share Functionality",
        "Notification Center"
      ]
    },
    {
      category: "Visual Features",
      icon: <Star className="w-6 h-6" />,
      items: [
        "High-Quality Images",
        "Gradient Backgrounds",
        "Hover Animations",
        "Badge System",
        "Rating Display",
        "Modern UI Components"
      ]
    },
    {
      category: "Admin Dashboard",
      icon: <Shield className="w-6 h-6" />,
      items: [
        "Real-time Dashboard",
        "Hotel Management",
        "Room Management",
        "Booking Analytics",
        "User Management",
        "Data Seeding"
      ]
    }
  ];

  const stats = [
    { label: "Room Types", value: "10+", icon: <Hotel className="w-5 h-5" /> },
    { label: "Hotels", value: "5+", icon: <Globe className="w-5 h-5" /> },
    { label: "Features", value: "30+", icon: <Zap className="w-5 h-5" /> },
    { label: "Components", value: "25+", icon: <CheckCircle className="w-5 h-5" /> }
  ];

  return (
    <section className={cn('py-16 md:py-24 bg-gradient-to-br from-purple-50 to-pink-50', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Complete Hotel Booking System
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              A comprehensive solution with touch-friendly interface, room management, and booking functionality
            </p>
          </div>
        </FadeIn>

        {/* Stats */}
        <FadeIn delay={100}>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="flex items-center justify-center mb-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-primary">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </FadeIn>

        {/* Feature Categories */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <FadeIn key={index} delay={200 + index * 100}>
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      {feature.icon}
                    </div>
                    {feature.category}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {feature.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="text-sm">{item}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </FadeIn>
          ))}
        </div>

        {/* Key Highlights */}
        <FadeIn delay={600}>
          <div className="mt-12">
            <Card className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
              <CardHeader>
                <CardTitle className="text-2xl font-serif text-center">Key Highlights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <Smartphone className="w-5 h-5" />
                      Touch-Optimized Experience
                    </h4>
                    <ul className="space-y-1 text-sm text-primary-foreground/90">
                      <li>• Swipe gestures for image navigation</li>
                      <li>• Touch-friendly button sizes</li>
                      <li>• Smooth scrolling animations</li>
                      <li>• Mobile-first responsive design</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <Hotel className="w-5 h-5" />
                      Comprehensive Room System
                    </h4>
                    <ul className="space-y-1 text-sm text-primary-foreground/90">
                      <li>• 10 different room types</li>
                      <li>• Detailed amenities and features</li>
                      <li>• Real-time availability status</li>
                      <li>• Price comparison and discounts</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </FadeIn>

        {/* Navigation Links */}
        <FadeIn delay={700}>
          <div className="mt-12 text-center">
            <h3 className="text-xl font-semibold mb-6">Explore the System</h3>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge 
                variant="outline" 
                className="px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                onClick={() => document.getElementById('rooms')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <Hotel className="w-4 h-4 mr-2" />
                View Rooms
              </Badge>
              <Badge 
                variant="outline" 
                className="px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                onClick={() => window.location.href = '/touch-booking'}
              >
                <Smartphone className="w-4 h-4 mr-2" />
                Touch Booking
              </Badge>
              <Badge 
                variant="outline" 
                className="px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                onClick={() => document.getElementById('booking')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <Calendar className="w-4 h-4 mr-2" />
                Book Now
              </Badge>
              <Badge 
                variant="outline" 
                className="px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                onClick={() => window.location.href = '/admin/login'}
              >
                <Shield className="w-4 h-4 mr-2" />
                Admin Panel
              </Badge>
            </div>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default FeatureSummary;
