// User bookings page
import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import StaticBookingHistory from '@/components/StaticBookingHistory';
import { withUserAuth } from '@/contexts/AuthContext';

const BookingsPage: React.FC = () => {
  return (
    <main className="relative min-h-screen bg-background">
      <Navbar />
      <div className="container mx-auto px-4 md:px-6 py-24">
        <StaticBookingHistory />
      </div>
      <Footer />
    </main>
  );
};

export default withUserAuth(BookingsPage);
