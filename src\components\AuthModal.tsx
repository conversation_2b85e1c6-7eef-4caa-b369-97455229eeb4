// Authentication modal for user and admin login/registration
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  User, 
  Mail, 
  Lock, 
  Phone, 
  MapPin, 
  Eye, 
  EyeOff,
  Shield,
  UserPlus,
  LogIn,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { signInUser, signUpUser, signInAdmin } from '@/services/authService';
import { useAuth } from '@/contexts/AuthContext';
import { Link } from 'react-router-dom';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'user' | 'admin';
}

const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, defaultTab = 'user' }) => {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { refreshUserData, refreshAdminData } = useAuth();

  // User form state
  const [userForm, setUserForm] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    address: '',
    city: ''
  });

  // Admin form state
  const [adminForm, setAdminForm] = useState({
    email: '',
    password: ''
  });

  const handleUserInputChange = (field: string, value: string) => {
    setUserForm(prev => ({ ...prev, [field]: value }));
  };

  const handleAdminInputChange = (field: string, value: string) => {
    setAdminForm(prev => ({ ...prev, [field]: value }));
  };

  const validateUserForm = () => {
    if (!userForm.email || !userForm.password) {
      toast.error('Email and password are required');
      return false;
    }

    if (authMode === 'register') {
      if (!userForm.name || !userForm.phone) {
        toast.error('Name and phone are required for registration');
        return false;
      }

      if (userForm.password !== userForm.confirmPassword) {
        toast.error('Passwords do not match');
        return false;
      }

      if (userForm.password.length < 6) {
        toast.error('Password must be at least 6 characters');
        return false;
      }
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userForm.email)) {
      toast.error('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const validateAdminForm = () => {
    if (!adminForm.email || !adminForm.password) {
      toast.error('Email and password are required');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(adminForm.email)) {
      toast.error('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleUserAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateUserForm()) return;

    setLoading(true);
    
    try {
      if (authMode === 'login') {
        await signInUser(userForm.email, userForm.password);
        toast.success('Signed in successfully!');
      } else {
        await signUpUser({
          email: userForm.email,
          password: userForm.password,
          name: userForm.name,
          phone: userForm.phone,
          address: userForm.address,
          city: userForm.city
        });
        toast.success('Account created successfully!');
      }
      
      await refreshUserData();
      onClose();
      resetForms();
    } catch (error: any) {
      toast.error(error.message || 'Authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const handleAdminAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateAdminForm()) return;

    setLoading(true);
    
    try {
      await signInAdmin(adminForm.email, adminForm.password);
      toast.success('Admin signed in successfully!');
      await refreshAdminData();
      onClose();
      resetForms();
    } catch (error: any) {
      toast.error(error.message || 'Admin authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const resetForms = () => {
    setUserForm({
      name: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      address: '',
      city: ''
    });
    setAdminForm({
      email: '',
      password: ''
    });
    setAuthMode('login');
    setShowPassword(false);
  };

  const handleClose = () => {
    resetForms();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            Welcome to One Touch Hotels
          </DialogTitle>
          <DialogDescription className="text-center">
            Sign in to your account or create a new one
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'user' | 'admin')} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="user" className="flex items-center gap-2">
              <User className="w-4 h-4" />
              User
            </TabsTrigger>
            <TabsTrigger value="admin" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Admin
            </TabsTrigger>
          </TabsList>

          {/* User Authentication */}
          <TabsContent value="user" className="space-y-4">
            <div className="flex justify-center mb-4">
              <div className="flex bg-muted rounded-lg p-1">
                <Button
                  variant={authMode === 'login' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setAuthMode('login')}
                  className="flex items-center gap-2"
                >
                  <LogIn className="w-4 h-4" />
                  Sign In
                </Button>
                <Button
                  variant={authMode === 'register' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setAuthMode('register')}
                  className="flex items-center gap-2"
                >
                  <UserPlus className="w-4 h-4" />
                  Sign Up
                </Button>
              </div>
            </div>

            <form onSubmit={handleUserAuth} className="space-y-4">
              {authMode === 'register' && (
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="name">Full Name *</Label>
                    <div className="relative">
                      <Input
                        id="name"
                        type="text"
                        value={userForm.name}
                        onChange={(e) => handleUserInputChange('name', e.target.value)}
                        placeholder="Enter your full name"
                        className="pl-10"
                        required
                      />
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <div className="relative">
                      <Input
                        id="phone"
                        type="tel"
                        value={userForm.phone}
                        onChange={(e) => handleUserInputChange('phone', e.target.value)}
                        placeholder="Enter your phone number"
                        className="pl-10"
                        required
                      />
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    </div>
                  </div>
                </div>
              )}

              <div>
                <Label htmlFor="email">Email Address *</Label>
                <div className="relative">
                  <Input
                    id="email"
                    type="email"
                    value={userForm.email}
                    onChange={(e) => handleUserInputChange('email', e.target.value)}
                    placeholder="Enter your email"
                    className="pl-10"
                    required
                  />
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                </div>
              </div>

              <div>
                <Label htmlFor="password">Password *</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={userForm.password}
                    onChange={(e) => handleUserInputChange('password', e.target.value)}
                    placeholder="Enter your password"
                    className="pl-10 pr-10"
                    required
                  />
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
              </div>

              {authMode === 'register' && (
                <>
                  <div>
                    <Label htmlFor="confirmPassword">Confirm Password *</Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        type={showPassword ? 'text' : 'password'}
                        value={userForm.confirmPassword}
                        onChange={(e) => handleUserInputChange('confirmPassword', e.target.value)}
                        placeholder="Confirm your password"
                        className="pl-10"
                        required
                      />
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="address">Address</Label>
                      <div className="relative">
                        <Input
                          id="address"
                          type="text"
                          value={userForm.address}
                          onChange={(e) => handleUserInputChange('address', e.target.value)}
                          placeholder="Enter your address"
                          className="pl-10"
                        />
                        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        type="text"
                        value={userForm.city}
                        onChange={(e) => handleUserInputChange('city', e.target.value)}
                        placeholder="Enter your city"
                      />
                    </div>
                  </div>
                </>
              )}

              <Button type="submit" className="w-full btn-primary" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {authMode === 'login' ? 'Signing In...' : 'Creating Account...'}
                  </>
                ) : (
                  <>
                    {authMode === 'login' ? <LogIn className="w-4 h-4 mr-2" /> : <UserPlus className="w-4 h-4 mr-2" />}
                    {authMode === 'login' ? 'Sign In' : 'Create Account'}
                  </>
                )}
              </Button>
            </form>
          </TabsContent>

          {/* Admin Authentication */}
          <TabsContent value="admin" className="space-y-4">
            <div className="text-center mb-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-2">
                <Shield className="w-8 h-8 text-primary" />
              </div>
              <p className="text-sm text-muted-foreground">
                Admin access only. Please contact support if you need admin privileges.
              </p>
            </div>

            <form onSubmit={handleAdminAuth} className="space-y-4">
              <div>
                <Label htmlFor="adminEmail">Admin Email *</Label>
                <div className="relative">
                  <Input
                    id="adminEmail"
                    type="email"
                    value={adminForm.email}
                    onChange={(e) => handleAdminInputChange('email', e.target.value)}
                    placeholder="Enter admin email"
                    className="pl-10"
                    required
                  />
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                </div>
              </div>

              <div>
                <Label htmlFor="adminPassword">Admin Password *</Label>
                <div className="relative">
                  <Input
                    id="adminPassword"
                    type={showPassword ? 'text' : 'password'}
                    value={adminForm.password}
                    onChange={(e) => handleAdminInputChange('password', e.target.value)}
                    placeholder="Enter admin password"
                    className="pl-10 pr-10"
                    required
                  />
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
              </div>

              <Button type="submit" className="w-full btn-primary" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Signing In...
                  </>
                ) : (
                  <>
                    <Shield className="w-4 h-4 mr-2" />
                    Admin Sign In
                  </>
                )}
              </Button>
            </form>
          </TabsContent>
        </Tabs>

        {/* Footer with links to dedicated pages */}
        <div className="mt-6 pt-4 border-t border-gray-200 text-center">
          <p className="text-sm text-gray-600 mb-3">
            Prefer a full-page experience?
          </p>
          <div className="flex justify-center gap-4">
            <Link
              to="/signin"
              className="text-sm text-primary hover:underline"
              onClick={handleClose}
            >
              Go to Sign In Page
            </Link>
            <span className="text-gray-300">|</span>
            <Link
              to="/signup"
              className="text-sm text-primary hover:underline"
              onClick={handleClose}
            >
              Go to Sign Up Page
            </Link>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuthModal;
