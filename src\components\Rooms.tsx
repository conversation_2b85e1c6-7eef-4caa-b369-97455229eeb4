import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Calendar,
  Users,
  Wifi,
  Coffee,
  Car,
  Tv,
  Bed,
  Bath,
  Wind,
  Utensils,
  Shield,
  Sparkles,
  Phone
} from 'lucide-react';
import { getTiruvannamalaiHotels, getHotelRooms } from '@/services/hotelService';
import { Hotel, Room } from '@/types/hotel';
import { toast } from 'sonner';

interface RoomsProps {
  className?: string;
  onBookRoom?: (hotelId: string, roomId: string) => void;
}

const Rooms: React.FC<RoomsProps> = ({ className, onBookRoom }) => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [allRooms, setAllRooms] = useState<Room[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null);

  useEffect(() => {
    loadHotelsAndRooms();
  }, []);

  const loadHotelsAndRooms = async () => {
    try {
      setLoading(true);
      const hotelsData = await getTiruvannamalaiHotels();
      setHotels(hotelsData);

      // Load rooms for all hotels
      const allRoomsData: Room[] = [];
      for (const hotel of hotelsData) {
        const hotelRooms = await getHotelRooms(hotel.id);
        allRoomsData.push(...hotelRooms);
      }
      setAllRooms(allRoomsData);
    } catch (error) {
      console.error('Error loading hotels and rooms:', error);
      toast.error('Failed to load rooms');
    } finally {
      setLoading(false);
    }
  };

  const handleBookNow = (room: Room) => {
    if (onBookRoom) {
      onBookRoom(room.hotelId, room.id);
    } else {
      // Scroll to booking section
      const bookingSection = document.getElementById('booking');
      if (bookingSection) {
        bookingSection.scrollIntoView({ behavior: 'smooth' });
        toast.success(`Selected ${room.name}. Please complete the booking form below.`);
      }
    }
  };

  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi')) return <Wifi className="w-4 h-4" />;
    if (amenityLower.includes('tv')) return <Tv className="w-4 h-4" />;
    if (amenityLower.includes('coffee')) return <Coffee className="w-4 h-4" />;
    if (amenityLower.includes('parking')) return <Car className="w-4 h-4" />;
    if (amenityLower.includes('ac') || amenityLower.includes('air conditioning')) return <Wind className="w-4 h-4" />;
    if (amenityLower.includes('restaurant') || amenityLower.includes('dining')) return <Utensils className="w-4 h-4" />;
    if (amenityLower.includes('bathroom')) return <Bath className="w-4 h-4" />;
    if (amenityLower.includes('bed')) return <Bed className="w-4 h-4" />;
    if (amenityLower.includes('security') || amenityLower.includes('safe')) return <Shield className="w-4 h-4" />;
    return <Sparkles className="w-4 h-4" />;
  };

  const filteredRooms = selectedHotel === 'all'
    ? allRooms
    : allRooms.filter(room => room.hotelId === selectedHotel);

  if (loading) {
    return (
      <section id="rooms" className={cn('py-16 md:py-24 bg-background', className)}>
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="rooms" className={cn('py-16 md:py-24 bg-background', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Premium Rooms & Suites in Tiruvannamalai
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover comfort and luxury in the spiritual heart of Tamil Nadu. Choose from our carefully selected accommodations.
            </p>
          </div>
        </FadeIn>

        {/* Hotel Filter */}
        <FadeIn delay={100}>
          <div className="flex justify-center mb-8">
            <div className="w-full max-w-md">
              <Select value={selectedHotel} onValueChange={setSelectedHotel}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by hotel" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Hotels</SelectItem>
                  {hotels.map((hotel) => (
                    <SelectItem key={hotel.id} value={hotel.id}>
                      {hotel.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </FadeIn>

        {filteredRooms.length === 0 ? (
          <FadeIn>
            <div className="text-center py-12">
              <Bed className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-xl font-semibold mb-2">No rooms available</h3>
              <p className="text-muted-foreground">
                {selectedHotel === 'all'
                  ? 'Please check back later or contact us directly.'
                  : 'Try selecting a different hotel or view all hotels.'}
              </p>
            </div>
          </FadeIn>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRooms.map((room, index) => {
              const hotel = hotels.find(h => h.id === room.hotelId);
              return (
                <FadeIn key={room.id} delay={index * 100}>
                  <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 hover:scale-105">
                    <div className="relative">
                      <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                        {room.images && room.images.length > 0 ? (
                          <img
                            src={room.images[0]}
                            alt={room.name}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : null}
                        <div className="hidden flex-col items-center justify-center text-muted-foreground">
                          <Bed className="w-12 h-12 mb-2" />
                          <span className="text-sm">Room Image</span>
                        </div>
                      </div>
                      <Badge className="absolute top-4 right-4 bg-primary text-primary-foreground">
                        ₹{room.price.toLocaleString()}/night
                      </Badge>
                      <Badge className="absolute top-4 left-4 bg-black/70 text-white">
                        {hotel?.name || 'Hotel'}
                      </Badge>
                    </div>

                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-start justify-between">
                        <div>
                          <span className="text-lg">{room.name}</span>
                          <Badge variant="outline" className="ml-2 capitalize text-xs">
                            {room.type}
                          </Badge>
                        </div>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Users className="w-4 h-4 mr-1" />
                          {room.capacity}
                        </div>
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">
                        {room.size} • {room.description}
                      </p>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2 text-sm">Room Features</h4>
                          <div className="flex flex-wrap gap-1">
                            {room.amenities.slice(0, 4).map((amenity, i) => (
                              <div key={i} className="flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-full">
                                {getAmenityIcon(amenity)}
                                <span className="truncate max-w-20">{amenity}</span>
                              </div>
                            ))}
                            {room.amenities.length > 4 && (
                              <Badge variant="outline" className="text-xs">
                                +{room.amenities.length - 4} more
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <span>Availability:</span>
                          <span className="font-medium">
                            {room.availableRooms}/{room.totalRooms} rooms
                          </span>
                        </div>

                        <div className="flex gap-2">
                          <Button
                            className="flex-1"
                            onClick={() => handleBookNow(room)}
                            disabled={room.availableRooms === 0}
                          >
                            {room.availableRooms === 0 ? 'Fully Booked' : 'Book Now'}
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => setSelectedRoom(selectedRoom === room.id ? null : room.id)}
                          >
                            <Calendar className="w-4 h-4" />
                          </Button>
                        </div>

                        {room.bedType && (
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <Bed className="w-3 h-3" />
                            {room.bedType}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </FadeIn>
              );
            })}
          </div>
        )}

        <FadeIn delay={400}>
          <div className="text-center mt-12">
            <p className="text-sm text-muted-foreground mb-4">
              Need help choosing the perfect room for your spiritual journey?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline">
                <Phone className="w-4 h-4 mr-2" />
                Contact Our Concierge
              </Button>
              <Button onClick={() => document.getElementById('booking')?.scrollIntoView({ behavior: 'smooth' })}>
                <Calendar className="w-4 h-4 mr-2" />
                Book Your Stay
              </Button>
            </div>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default Rooms;