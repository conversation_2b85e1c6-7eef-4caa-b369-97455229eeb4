// Admin setup component for creating initial admin accounts
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  User, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from 'sonner';
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { Admin } from '@/types/hotel';

interface AdminAccount {
  email: string;
  password: string;
  name: string;
  role: 'super_admin' | 'admin' | 'manager';
  permissions: string[];
  status?: 'pending' | 'creating' | 'success' | 'error';
  uid?: string;
  error?: string;
}

const AdminSetup: React.FC = () => {
  const [showPasswords, setShowPasswords] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [adminAccounts, setAdminAccounts] = useState<AdminAccount[]>([
    {
      email: '<EMAIL>',
      password: 'tamiladmin',
      name: 'Tamil Administrator',
      role: 'super_admin',
      permissions: [
        'manage_users',
        'manage_admins',
        'manage_hotels',
        'manage_bookings',
        'view_analytics',
        'manage_settings',
        'manage_payments',
        'manage_reviews',
        'system_admin'
      ],
      status: 'pending'
    }
  ]);

  const createAdminAccount = async (adminData: AdminAccount): Promise<AdminAccount> => {
    try {
      console.log(`Creating admin account for: ${adminData.email}`);
      
      // Create Firebase user
      const userCredential = await createUserWithEmailAndPassword(
        auth, 
        adminData.email, 
        adminData.password
      );
      const user = userCredential.user;

      // Update Firebase profile
      await updateProfile(user, {
        displayName: adminData.name
      });

      // Create admin document in Firestore
      const adminDoc: Admin = {
        id: user.uid,
        name: adminData.name,
        email: adminData.email,
        role: adminData.role,
        permissions: adminData.permissions,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLogin: new Date()
      };

      await setDoc(doc(db, 'admins', user.uid), adminDoc);
      
      console.log(`✅ Admin account created successfully: ${adminData.email}`);
      
      return {
        ...adminData,
        status: 'success',
        uid: user.uid
      };
    } catch (error: any) {
      console.error(`❌ Error creating admin account ${adminData.email}:`, error.message);
      
      return {
        ...adminData,
        status: 'error',
        error: error.message
      };
    }
  };

  const handleCreateAllAdmins = async () => {
    setIsCreating(true);
    
    try {
      const updatedAccounts = [...adminAccounts];
      
      for (let i = 0; i < updatedAccounts.length; i++) {
        // Update status to creating
        updatedAccounts[i] = { ...updatedAccounts[i], status: 'creating' };
        setAdminAccounts([...updatedAccounts]);
        
        // Create the admin account
        const result = await createAdminAccount(updatedAccounts[i]);
        updatedAccounts[i] = result;
        setAdminAccounts([...updatedAccounts]);
        
        // Small delay between creations
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      const successCount = updatedAccounts.filter(acc => acc.status === 'success').length;
      const errorCount = updatedAccounts.filter(acc => acc.status === 'error').length;
      
      if (successCount > 0) {
        toast.success(`Successfully created ${successCount} admin account(s)`);
      }
      
      if (errorCount > 0) {
        toast.error(`Failed to create ${errorCount} admin account(s)`);
      }
      
    } catch (error) {
      toast.error('Failed to create admin accounts');
      console.error('Admin creation error:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'creating':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <User className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'creating':
        return <Badge className="bg-blue-500">Creating...</Badge>;
      case 'success':
        return <Badge className="bg-green-500">Created</Badge>;
      case 'error':
        return <Badge className="bg-red-500">Error</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center">
              <Shield className="w-5 h-5 text-white" />
            </div>
            Admin Account Setup
          </CardTitle>
          <p className="text-muted-foreground">
            Create initial admin accounts for One Touch Hotels system. These accounts will have administrative privileges.
          </p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                onClick={handleCreateAllAdmins}
                disabled={isCreating}
                className="btn-primary"
              >
                {isCreating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating Accounts...
                  </>
                ) : (
                  <>
                    <Shield className="w-4 h-4 mr-2" />
                    Create All Admin Accounts
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setShowPasswords(!showPasswords)}
                className="flex items-center gap-2"
              >
                {showPasswords ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {showPasswords ? 'Hide' : 'Show'} Passwords
              </Button>
            </div>
          </div>

          {/* Admin Accounts */}
          <div className="grid gap-4">
            {adminAccounts.map((admin, index) => (
              <Card key={admin.email} className="border-l-4 border-l-primary">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      {getStatusIcon(admin.status)}
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{admin.name}</h3>
                          {getStatusBadge(admin.status)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <div className="flex items-center gap-2">
                            <span>📧 {admin.email}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(admin.email)}
                              className="h-6 w-6 p-0"
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <span>🔑 {showPasswords ? admin.password : '••••••••••'}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(admin.password)}
                              className="h-6 w-6 p-0"
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                          </div>
                          <div className="mt-1">
                            <span>👤 Role: {admin.role}</span>
                          </div>
                          {admin.uid && (
                            <div className="mt-1">
                              <span className="text-xs">UID: {admin.uid}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-xs text-muted-foreground">
                        {admin.permissions.length} permissions
                      </div>
                    </div>
                  </div>
                  
                  {admin.error && (
                    <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                      Error: {admin.error}
                    </div>
                  )}
                  
                  {admin.status === 'success' && (
                    <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-600">
                      ✅ Admin account created successfully! You can now sign in with these credentials.
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Instructions */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <h4 className="font-semibold text-blue-900 mb-2">📋 Next Steps:</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-blue-800">
                <li>Click "Create All Admin Accounts" to set up the admin accounts</li>
                <li>Wait for all accounts to be created successfully</li>
                <li>Use the credentials above to sign in as an admin</li>
                <li>Change the default passwords after first login</li>
                <li>Deploy the Firestore rules: <code className="bg-blue-100 px-1 rounded">firebase deploy --only firestore:rules</code></li>
              </ol>
            </CardContent>
          </Card>

          {/* Security Warning */}
          <Card className="bg-orange-50 border-orange-200">
            <CardContent className="p-4">
              <h4 className="font-semibold text-orange-900 mb-2">⚠️ Security Notice:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-orange-800">
                <li>Change all default passwords immediately after first login</li>
                <li>Enable two-factor authentication for all admin accounts</li>
                <li>Regularly audit admin permissions and access</li>
                <li>Remove this setup component in production</li>
              </ul>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminSetup;
