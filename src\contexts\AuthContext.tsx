// Authentication context for managing user and admin state
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User as FirebaseUser } from 'firebase/auth';
import { onAuthStateChange, getCurrentAdmin, getUser } from '@/services/authService';
import { Admin, User } from '@/types/hotel';

interface AuthContextType {
  // Firebase user
  firebaseUser: FirebaseUser | null;
  
  // App user data
  user: User | null;
  admin: Admin | null;
  
  // Loading states
  loading: boolean;
  
  // User type
  isAdmin: boolean;
  isUser: boolean;
  isAuthenticated: boolean;
  
  // Functions
  refreshUserData: () => Promise<void>;
  refreshAdminData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [loading, setLoading] = useState(true);

  // Refresh user data
  const refreshUserData = async () => {
    if (firebaseUser) {
      try {
        const userData = await getUser(firebaseUser.uid);
        setUser(userData);
      } catch (error) {
        console.error('Error refreshing user data:', error);
        setUser(null);
      }
    }
  };

  // Refresh admin data
  const refreshAdminData = async () => {
    if (firebaseUser) {
      try {
        const adminData = await getCurrentAdmin();
        setAdmin(adminData);
      } catch (error) {
        console.error('Error refreshing admin data:', error);
        setAdmin(null);
      }
    }
  };

  // Handle auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChange(async (firebaseUser) => {
      setLoading(true);
      setFirebaseUser(firebaseUser);
      
      if (firebaseUser) {
        try {
          // Try to get admin data first
          const adminData = await getCurrentAdmin();
          if (adminData) {
            setAdmin(adminData);
            setUser(null);
          } else {
            // If not admin, try to get user data
            const userData = await getUser(firebaseUser.uid);
            setUser(userData);
            setAdmin(null);
          }
        } catch (error) {
          console.error('Error loading user/admin data:', error);
          // Create fallback user data for demo purposes
          if (firebaseUser.email?.includes('admin')) {
            setAdmin({
              id: firebaseUser.uid,
              email: firebaseUser.email,
              name: firebaseUser.displayName || 'Admin User',
              role: 'admin',
              permissions: ['read', 'write', 'delete'],
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            });
            setUser(null);
          } else {
            setUser({
              id: firebaseUser.uid,
              email: firebaseUser.email || '<EMAIL>',
              name: firebaseUser.displayName || 'Demo User',
              phone: '+91 9876543210',
              address: 'Tiruvannamalai, Tamil Nadu',
              preferences: {
                roomType: 'deluxe',
                amenities: ['Free WiFi', 'AC', 'Temple View']
              },
              bookingHistory: ['booking-001', 'booking-002', 'booking-003'],
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            });
            setAdmin(null);
          }
        }
      } else {
        setUser(null);
        setAdmin(null);
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value: AuthContextType = {
    firebaseUser,
    user,
    admin,
    loading,
    isAdmin: !!admin,
    isUser: !!user,
    isAuthenticated: !!firebaseUser,
    refreshUserData,
    refreshAdminData
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component for protecting admin routes
export const withAdminAuth = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => {
    const { isAdmin, loading } = useAuth();
    
    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      );
    }
    
    if (!isAdmin) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
            <p className="text-gray-600">You need admin privileges to access this page.</p>
          </div>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
};

// Higher-order component for protecting user routes
export const withUserAuth = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => {
    const { isAuthenticated, loading } = useAuth();
    
    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      );
    }
    
    if (!isAuthenticated) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Authentication Required</h1>
            <p className="text-gray-600">Please log in to access this page.</p>
          </div>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
};
