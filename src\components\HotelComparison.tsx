// Hotel comparison component
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Star, 
  MapPin, 
  IndianRupee, 
  Users, 
  Phone,
  Mail,
  Clock,
  Wifi,
  Car,
  Utensils,
  Dumbbell,
  Waves,
  Coffee,
  Shield,
  Check,
  X,
  Plus
} from 'lucide-react';
import { getTiruvannamalaiHotels } from '@/services/hotelService';
import { Hotel } from '@/types/hotel';
import { toast } from 'sonner';

interface HotelComparisonProps {
  className?: string;
  preSelectedHotels?: string[];
  onBookHotel?: (hotelId: string) => void;
}

const HotelComparison: React.FC<HotelComparisonProps> = ({ 
  className, 
  preSelectedHotels = [],
  onBookHotel 
}) => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [selectedHotels, setSelectedHotels] = useState<Hotel[]>([]);
  const [availableHotels, setAvailableHotels] = useState<Hotel[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHotels();
  }, []);

  useEffect(() => {
    if (hotels.length > 0 && preSelectedHotels.length > 0) {
      const preSelected = hotels.filter(h => preSelectedHotels.includes(h.id));
      setSelectedHotels(preSelected);
    }
  }, [hotels, preSelectedHotels]);

  const loadHotels = async () => {
    try {
      setLoading(true);
      const hotelsData = await getTiruvannamalaiHotels();
      setHotels(hotelsData);
      setAvailableHotels(hotelsData);
    } catch (error) {
      console.error('Error loading hotels:', error);
      toast.error('Failed to load hotels');
    } finally {
      setLoading(false);
    }
  };

  const addHotelToComparison = (hotelId: string) => {
    if (selectedHotels.length >= 3) {
      toast.error('You can compare up to 3 hotels at a time');
      return;
    }

    const hotel = hotels.find(h => h.id === hotelId);
    if (hotel && !selectedHotels.find(h => h.id === hotelId)) {
      setSelectedHotels(prev => [...prev, hotel]);
    }
  };

  const removeHotelFromComparison = (hotelId: string) => {
    setSelectedHotels(prev => prev.filter(h => h.id !== hotelId));
  };

  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi')) return <Wifi className="w-4 h-4" />;
    if (amenityLower.includes('parking')) return <Car className="w-4 h-4" />;
    if (amenityLower.includes('restaurant')) return <Utensils className="w-4 h-4" />;
    if (amenityLower.includes('gym') || amenityLower.includes('fitness')) return <Dumbbell className="w-4 h-4" />;
    if (amenityLower.includes('pool')) return <Waves className="w-4 h-4" />;
    if (amenityLower.includes('coffee')) return <Coffee className="w-4 h-4" />;
    if (amenityLower.includes('security')) return <Shield className="w-4 h-4" />;
    return null;
  };

  const getAllAmenities = () => {
    const allAmenities = new Set<string>();
    selectedHotels.forEach(hotel => {
      hotel.amenities.forEach(amenity => allAmenities.add(amenity));
    });
    return Array.from(allAmenities).sort();
  };

  const hasAmenity = (hotel: Hotel, amenity: string) => {
    return hotel.amenities.some(a => a.toLowerCase() === amenity.toLowerCase());
  };

  const handleBookHotel = (hotelId: string) => {
    if (onBookHotel) {
      onBookHotel(hotelId);
    } else {
      const bookingSection = document.getElementById('booking');
      if (bookingSection) {
        bookingSection.scrollIntoView({ behavior: 'smooth' });
        const hotel = selectedHotels.find(h => h.id === hotelId);
        toast.success(`Selected ${hotel?.name}. Complete your booking below.`);
      }
    }
  };

  if (loading) {
    return (
      <section className={cn('py-16 md:py-24 bg-muted/30', className)}>
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="comparison" className={cn('py-16 md:py-24 bg-muted/30', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Compare Hotels
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Compare features, amenities, and prices to find the perfect hotel for your stay in Tiruvannamalai
            </p>
          </div>
        </FadeIn>

        {/* Hotel Selection */}
        {selectedHotels.length < 3 && (
          <FadeIn delay={100}>
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="w-5 h-5" />
                  Add Hotel to Compare ({selectedHotels.length}/3)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Select onValueChange={addHotelToComparison}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a hotel to add to comparison" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableHotels
                      .filter(hotel => !selectedHotels.find(sh => sh.id === hotel.id))
                      .map((hotel) => (
                        <SelectItem key={hotel.id} value={hotel.id}>
                          {hotel.name} - ★{hotel.rating} (₹{hotel.priceRange.min.toLocaleString()}+)
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </CardContent>
            </Card>
          </FadeIn>
        )}

        {selectedHotels.length === 0 ? (
          <FadeIn delay={200}>
            <Card>
              <CardContent className="text-center py-12">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <Plus className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Start Comparing Hotels</h3>
                <p className="text-muted-foreground mb-4">
                  Select hotels from the dropdown above to compare their features, amenities, and prices.
                </p>
              </CardContent>
            </Card>
          </FadeIn>
        ) : (
          <div className="space-y-8">
            {/* Hotel Cards Comparison */}
            <FadeIn delay={200}>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {selectedHotels.map((hotel, index) => (
                  <Card key={hotel.id} className="relative">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute top-2 right-2 z-10"
                      onClick={() => removeHotelFromComparison(hotel.id)}
                    >
                      <X className="w-4 h-4" />
                    </Button>

                    <div className="relative">
                      <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                        {hotel.images && hotel.images.length > 0 ? (
                          <img 
                            src={hotel.images[0]} 
                            alt={hotel.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <MapPin className="w-12 h-12 mb-2" />
                            <span className="text-sm">Hotel Image</span>
                          </div>
                        )}
                      </div>
                      
                      <Badge className="absolute top-4 left-4 bg-black/70 text-white">
                        <Star className="w-3 h-3 mr-1 fill-current" />
                        {hotel.rating}
                      </Badge>
                    </div>

                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-xl font-semibold mb-2">{hotel.name}</h3>
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {hotel.description}
                          </p>
                        </div>

                        <div className="space-y-2 text-sm">
                          <div className="flex items-center gap-2">
                            <MapPin className="w-4 h-4 text-muted-foreground" />
                            <span className="truncate">{hotel.address}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Phone className="w-4 h-4 text-muted-foreground" />
                            <span>{hotel.phone}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Mail className="w-4 h-4 text-muted-foreground" />
                            <span className="truncate">{hotel.email}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-muted-foreground" />
                            <span>{hotel.totalReviews} reviews</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Price range:</span>
                          <span className="font-semibold">
                            ₹{hotel.priceRange.min.toLocaleString()} - ₹{hotel.priceRange.max.toLocaleString()}
                          </span>
                        </div>

                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>In: {hotel.checkInTime}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>Out: {hotel.checkOutTime}</span>
                          </div>
                        </div>

                        <Button 
                          className="w-full"
                          onClick={() => handleBookHotel(hotel.id)}
                        >
                          Book This Hotel
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </FadeIn>

            {/* Amenities Comparison Table */}
            {selectedHotels.length > 1 && (
              <FadeIn delay={400}>
                <Card>
                  <CardHeader>
                    <CardTitle>Amenities Comparison</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-3 font-medium">Amenity</th>
                            {selectedHotels.map(hotel => (
                              <th key={hotel.id} className="text-center p-3 font-medium min-w-32">
                                {hotel.name}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {getAllAmenities().map(amenity => (
                            <tr key={amenity} className="border-b hover:bg-muted/50">
                              <td className="p-3 flex items-center gap-2">
                                {getAmenityIcon(amenity)}
                                <span>{amenity}</span>
                              </td>
                              {selectedHotels.map(hotel => (
                                <td key={hotel.id} className="text-center p-3">
                                  {hasAmenity(hotel, amenity) ? (
                                    <Check className="w-5 h-5 text-green-500 mx-auto" />
                                  ) : (
                                    <X className="w-5 h-5 text-red-500 mx-auto" />
                                  )}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </FadeIn>
            )}
          </div>
        )}
      </div>
    </section>
  );
};

export default HotelComparison;
