
import React, { useEffect } from 'react';
import Navbar from '@/components/Navbar';
import Hero from '@/components/Hero';
import StaticHotelSearch from '@/components/StaticHotelSearch';
import StaticHotelShowcase from '@/components/StaticHotelShowcase';
import StaticHotelComparison from '@/components/StaticHotelComparison';
import StaticRooms from '@/components/StaticRooms';
import Amenities from '@/components/Amenities';
import SimpleBooking from '@/components/SimpleBooking';
import FeatureSummary from '@/components/FeatureSummary';
import About from '@/components/About';
import Footer from '@/components/Footer';

const Index = () => {

  useEffect(() => {
    // Smooth scroll behavior for anchor links
    const handleAnchorClick = (e: Event) => {
      e.preventDefault();
      const target = e.target as HTMLAnchorElement;
      const targetId = target.getAttribute('href')?.substring(1);
      if (!targetId) return;

      const targetElement = document.getElementById(targetId);
      if (targetElement) {
        window.scrollTo({
          top: targetElement.offsetTop - 80, // Account for header height
          behavior: 'smooth'
        });
      }
    };

    const anchors = document.querySelectorAll('a[href^="#"]');
    anchors.forEach(anchor => {
      anchor.addEventListener('click', handleAnchorClick);
    });

    return () => {
      anchors.forEach(anchor => {
        anchor.removeEventListener('click', handleAnchorClick);
      });
    };
  }, []);

  return (
    <main className="relative">
      <Navbar />
      <Hero />
      <StaticHotelSearch />
      <StaticHotelShowcase />
      <StaticHotelComparison />
      <StaticRooms />
      <Amenities />
      
      <FeatureSummary />
      <About />
      <Footer />
    </main>
  );
};

export default Index;
