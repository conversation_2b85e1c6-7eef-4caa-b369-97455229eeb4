// User profile page
import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import UserProfile from '@/components/UserProfile';
import { withUserAuth } from '@/contexts/AuthContext';

const ProfilePage: React.FC = () => {
  return (
    <main className="relative min-h-screen bg-background">
      <Navbar />
      <div className="container mx-auto px-4 md:px-6 py-24">
        <UserProfile />
      </div>
      <Footer />
    </main>
  );
};

export default withUserAuth(ProfilePage);
