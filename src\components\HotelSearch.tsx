// Advanced hotel search and filtering component
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  Star, 
  MapPin, 
  IndianRupee, 
  Users, 
  Calendar,
  SlidersHorizontal,
  X
} from 'lucide-react';
import { getTiruvannamalaiHotels, getHotelRooms, checkRoomAvailability } from '@/services/hotelService';
import { Hotel, Room, HotelSearchFilters } from '@/types/hotel';
import { toast } from 'sonner';

interface HotelSearchProps {
  className?: string;
  onHotelSelect?: (hotel: Hotel) => void;
  onBookingSelect?: (hotelId: string, roomId: string) => void;
}

const HotelSearch: React.FC<HotelSearchProps> = ({ 
  className, 
  onHotelSelect, 
  onBookingSelect 
}) => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [filteredHotels, setFilteredHotels] = useState<Hotel[]>([]);
  const [loading, setLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  
  const [searchFilters, setSearchFilters] = useState<HotelSearchFilters>({
    city: 'Tiruvannamalai',
    priceRange: { min: 500, max: 25000 },
    rating: 0
  });

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'price' | 'rating' | 'name'>('rating');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Common amenities for filtering
  const commonAmenities = [
    'Free WiFi',
    'Restaurant',
    'Parking',
    'Air Conditioning',
    'Swimming Pool',
    'Fitness Center',
    'Room Service',
    'Bar',
    'Spa',
    'Business Center',
    'Conference Rooms',
    'Laundry Service'
  ];

  useEffect(() => {
    loadHotels();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [hotels, searchQuery, selectedAmenities, searchFilters, sortBy, sortOrder]);

  const loadHotels = async () => {
    try {
      setLoading(true);
      const hotelsData = await getTiruvannamalaiHotels();
      setHotels(hotelsData);
    } catch (error) {
      console.error('Error loading hotels:', error);
      toast.error('Failed to load hotels');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...hotels];

    // Text search
    if (searchQuery) {
      filtered = filtered.filter(hotel =>
        hotel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        hotel.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        hotel.address.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Price range filter
    if (searchFilters.priceRange) {
      filtered = filtered.filter(hotel =>
        hotel.priceRange.min <= searchFilters.priceRange!.max &&
        hotel.priceRange.max >= searchFilters.priceRange!.min
      );
    }

    // Rating filter
    if (searchFilters.rating && searchFilters.rating > 0) {
      filtered = filtered.filter(hotel => hotel.rating >= searchFilters.rating!);
    }

    // Amenities filter
    if (selectedAmenities.length > 0) {
      filtered = filtered.filter(hotel =>
        selectedAmenities.every(amenity =>
          hotel.amenities.some(hotelAmenity =>
            hotelAmenity.toLowerCase().includes(amenity.toLowerCase())
          )
        )
      );
    }

    // Sort results
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'price':
          comparison = a.priceRange.min - b.priceRange.min;
          break;
        case 'rating':
          comparison = a.rating - b.rating;
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredHotels(filtered);
  };

  const handleAmenityToggle = (amenity: string) => {
    setSelectedAmenities(prev =>
      prev.includes(amenity)
        ? prev.filter(a => a !== amenity)
        : [...prev, amenity]
    );
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedAmenities([]);
    setSearchFilters({
      city: 'Tiruvannamalai',
      priceRange: { min: 500, max: 25000 },
      rating: 0
    });
  };

  const handleHotelSelect = (hotel: Hotel) => {
    if (onHotelSelect) {
      onHotelSelect(hotel);
    } else {
      // Default behavior - scroll to booking
      const bookingSection = document.getElementById('booking');
      if (bookingSection) {
        bookingSection.scrollIntoView({ behavior: 'smooth' });
        toast.success(`Selected ${hotel.name}. Complete your booking below.`);
      }
    }
  };

  if (loading) {
    return (
      <section className={cn('py-16 md:py-24 bg-background', className)}>
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="search" className={cn('py-16 md:py-24 bg-background', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Find Your Perfect Stay
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Search and compare hotels in Tiruvannamalai to find the perfect accommodation for your spiritual journey
            </p>
          </div>
        </FadeIn>

        {/* Search Bar */}
        <FadeIn delay={100}>
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search hotels by name, location, or description..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowFilters(!showFilters)}
                    className="flex items-center gap-2"
                  >
                    <SlidersHorizontal className="w-4 h-4" />
                    Filters
                    {(selectedAmenities.length > 0 || searchFilters.rating! > 0) && (
                      <Badge variant="secondary" className="ml-1">
                        {selectedAmenities.length + (searchFilters.rating! > 0 ? 1 : 0)}
                      </Badge>
                    )}
                  </Button>
                  {(searchQuery || selectedAmenities.length > 0 || searchFilters.rating! > 0) && (
                    <Button variant="outline" onClick={clearFilters}>
                      <X className="w-4 h-4 mr-2" />
                      Clear
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </FadeIn>

        {/* Advanced Filters */}
        {showFilters && (
          <FadeIn delay={200}>
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="w-5 h-5" />
                  Advanced Filters
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Price Range */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">
                    Price Range: ₹{searchFilters.priceRange?.min.toLocaleString()} - ₹{searchFilters.priceRange?.max.toLocaleString()}
                  </Label>
                  <Slider
                    value={[searchFilters.priceRange?.min || 500, searchFilters.priceRange?.max || 25000]}
                    onValueChange={([min, max]) => 
                      setSearchFilters(prev => ({ ...prev, priceRange: { min, max } }))
                    }
                    min={500}
                    max={25000}
                    step={500}
                    className="w-full"
                  />
                </div>

                {/* Rating Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Minimum Rating</Label>
                  <Select 
                    value={searchFilters.rating?.toString() || '0'} 
                    onValueChange={(value) => 
                      setSearchFilters(prev => ({ ...prev, rating: parseInt(value) }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Any Rating</SelectItem>
                      <SelectItem value="3">3+ Stars</SelectItem>
                      <SelectItem value="4">4+ Stars</SelectItem>
                      <SelectItem value="4.5">4.5+ Stars</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Amenities Filter */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Amenities</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {commonAmenities.map((amenity) => (
                      <div key={amenity} className="flex items-center space-x-2">
                        <Checkbox
                          id={amenity}
                          checked={selectedAmenities.includes(amenity)}
                          onCheckedChange={() => handleAmenityToggle(amenity)}
                        />
                        <Label htmlFor={amenity} className="text-sm">
                          {amenity}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Sort Options */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Sort By</Label>
                    <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="rating">Rating</SelectItem>
                        <SelectItem value="price">Price</SelectItem>
                        <SelectItem value="name">Name</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Order</Label>
                    <Select value={sortOrder} onValueChange={(value: any) => setSortOrder(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="desc">High to Low</SelectItem>
                        <SelectItem value="asc">Low to High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </FadeIn>
        )}

        {/* Search Results */}
        <FadeIn delay={300}>
          <div className="mb-6 flex justify-between items-center">
            <h3 className="text-xl font-semibold">
              {filteredHotels.length} hotel{filteredHotels.length !== 1 ? 's' : ''} found
            </h3>
            {filteredHotels.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Sorted by {sortBy} ({sortOrder === 'desc' ? 'high to low' : 'low to high'})
              </div>
            )}
          </div>
        </FadeIn>

        {/* Results Grid */}
        {filteredHotels.length === 0 ? (
          <FadeIn delay={400}>
            <Card>
              <CardContent className="text-center py-12">
                <Search className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-xl font-semibold mb-2">No hotels found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search criteria or filters to find more results.
                </p>
                <Button onClick={clearFilters}>Clear All Filters</Button>
              </CardContent>
            </Card>
          </FadeIn>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredHotels.map((hotel, index) => (
              <FadeIn key={hotel.id} delay={400 + index * 100}>
                <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <div className="relative">
                    <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      {hotel.images && hotel.images.length > 0 ? (
                        <img
                          src={hotel.images[0]}
                          alt={hotel.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                            e.currentTarget.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <div className="hidden flex-col items-center justify-center text-muted-foreground">
                        <MapPin className="w-12 h-12 mb-2" />
                        <span className="text-sm">Hotel Image</span>
                      </div>
                    </div>

                    {/* Rating Badge */}
                    <Badge className="absolute top-4 right-4 bg-black/70 text-white">
                      <Star className="w-3 h-3 mr-1 fill-current" />
                      {hotel.rating}
                    </Badge>

                    {/* Price Badge */}
                    <Badge className="absolute bottom-4 right-4 bg-primary text-primary-foreground">
                      <IndianRupee className="w-3 h-3 mr-1" />
                      ₹{hotel.priceRange.min.toLocaleString()}+
                    </Badge>
                  </div>

                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-xl font-semibold mb-2">{hotel.name}</h3>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {hotel.description}
                        </p>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-muted-foreground" />
                          <span className="truncate">{hotel.address}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-muted-foreground" />
                          <span>{hotel.totalReviews} reviews</span>
                        </div>
                      </div>

                      {/* Price Range */}
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Price range:</span>
                        <span className="font-semibold">
                          ₹{hotel.priceRange.min.toLocaleString()} - ₹{hotel.priceRange.max.toLocaleString()}
                        </span>
                      </div>

                      {/* Top Amenities */}
                      <div>
                        <div className="flex flex-wrap gap-1">
                          {hotel.amenities.slice(0, 3).map((amenity, i) => (
                            <Badge key={i} variant="outline" className="text-xs">
                              {amenity}
                            </Badge>
                          ))}
                          {hotel.amenities.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{hotel.amenities.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <Button
                          className="flex-1"
                          onClick={() => handleHotelSelect(hotel)}
                        >
                          Select Hotel
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => {
                            // Show hotel details or comparison
                            toast.info(`Viewing details for ${hotel.name}`);
                          }}
                        >
                          <Search className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </FadeIn>
            ))}
          </div>
        )}

        {/* Call to Action */}
        <FadeIn delay={600}>
          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              Can't find what you're looking for?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline">
                Contact Our Travel Experts
              </Button>
              <Button onClick={() => document.getElementById('booking')?.scrollIntoView({ behavior: 'smooth' })}>
                <Calendar className="w-4 h-4 mr-2" />
                Book Direct
              </Button>
            </div>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default HotelSearch;
