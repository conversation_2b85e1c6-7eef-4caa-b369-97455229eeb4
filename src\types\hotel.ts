// Type definitions for the hotel booking system

export interface Hotel {
  id: string;
  name: string;
  description: string;
  address: string;
  city: string; // Will be "Tiruvannamalai" for our use case
  state: string;
  country: string;
  pincode: string;
  phone: string;
  email: string;
  website?: string;
  images: string[]; // URLs to Firebase Storage
  amenities: string[];
  rating: number;
  totalReviews: number;
  priceRange: {
    min: number;
    max: number;
  };
  location: {
    latitude: number;
    longitude: number;
  };
  checkInTime: string;
  checkOutTime: string;
  policies: {
    cancellation: string;
    petPolicy: string;
    smokingPolicy: string;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Room {
  id: string;
  hotelId: string;
  name: string;
  description: string;
  type: 'single' | 'double' | 'suite' | 'family' | 'deluxe' | 'executive';
  capacity: number;
  size: string; // e.g., "35 sqm"
  price: number; // per night
  images: string[];
  amenities: string[];
  bedType: string;
  totalRooms: number; // Total number of this room type
  availableRooms: number; // Currently available
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Booking {
  id: string;
  hotelId: string;
  roomId: string;
  userId?: string; // Optional for guest bookings
  guestInfo: {
    name: string;
    email: string;
    phone: string;
    address?: string;
  };
  checkInDate: Date;
  checkOutDate: Date;
  numberOfGuests: number;
  numberOfRooms: number;
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'no-show';
  paymentStatus: 'pending' | 'paid' | 'refunded' | 'failed';
  paymentMethod?: string;
  specialRequests?: string;
  bookingSource: 'website' | 'phone' | 'walk-in' | 'admin';
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  address?: string;
  dateOfBirth?: Date;
  preferences: {
    roomType?: string;
    amenities?: string[];
  };
  bookingHistory: string[]; // Array of booking IDs
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Admin {
  id: string;
  email: string;
  name: string;
  role: 'super_admin' | 'hotel_admin' | 'staff';
  permissions: string[];
  hotelIds?: string[]; // For hotel-specific admins
  isActive: boolean;
  createdAt: Date;
  lastLogin?: Date;
}

export interface Review {
  id: string;
  hotelId: string;
  bookingId: string;
  userId?: string;
  guestName: string;
  rating: number; // 1-5
  title: string;
  comment: string;
  images?: string[];
  isVerified: boolean;
  isVisible: boolean;
  adminResponse?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Utility types for forms and API responses
export interface BookingFormData {
  checkInDate: string;
  checkOutDate: string;
  numberOfGuests: number;
  roomType: string;
  guestInfo: {
    name: string;
    email: string;
    phone: string;
  };
  specialRequests?: string;
}

export interface HotelSearchFilters {
  city?: string;
  checkIn?: Date;
  checkOut?: Date;
  guests?: number;
  priceRange?: {
    min: number;
    max: number;
  };
  amenities?: string[];
  rating?: number;
}

export interface BookingAvailability {
  roomId: string;
  available: boolean;
  availableRooms: number;
  price: number;
  totalPrice: number;
}
