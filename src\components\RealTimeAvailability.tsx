// Real-time room availability tracker component
import React, { useState, useEffect } from 'react';
import { RealtimeService, RoomAvailabilityUpdate } from '@/services/realtimeService';
import { getHotelRooms } from '@/services/hotelService';
import { Room } from '@/types/hotel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Bed, 
  Users, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';

interface RealTimeAvailabilityProps {
  hotelId: string;
  className?: string;
  showHeader?: boolean;
}

const RealTimeAvailability: React.FC<RealTimeAvailabilityProps> = ({ 
  hotelId, 
  className,
  showHeader = true 
}) => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [availabilityUpdates, setAvailabilityUpdates] = useState<RoomAvailabilityUpdate[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    loadRooms();
    subscribeToUpdates();

    return () => {
      const realtimeService = RealtimeService.getInstance();
      realtimeService.unsubscribe(`hotel_availability_${hotelId}`);
    };
  }, [hotelId]);

  const loadRooms = async () => {
    try {
      setLoading(true);
      const roomsData = await getHotelRooms(hotelId);
      setRooms(roomsData);
    } catch (error) {
      console.error('Error loading rooms:', error);
      toast.error('Failed to load room data');
    } finally {
      setLoading(false);
    }
  };

  const subscribeToUpdates = () => {
    const realtimeService = RealtimeService.getInstance();
    
    realtimeService.subscribeToHotelAvailability(hotelId, (updates) => {
      setAvailabilityUpdates(updates);
      setLastUpdate(new Date());
      
      // Update room data with latest availability
      setRooms(prevRooms => 
        prevRooms.map(room => {
          const update = updates.find(u => u.roomId === room.id);
          if (update) {
            return {
              ...room,
              availableRooms: update.availableRooms,
              totalRooms: update.totalRooms
            };
          }
          return room;
        })
      );
    });
  };

  const getAvailabilityStatus = (available: number, total: number) => {
    const percentage = (available / total) * 100;
    
    if (percentage === 0) return { status: 'sold-out', color: 'destructive', icon: AlertTriangle };
    if (percentage <= 20) return { status: 'low', color: 'destructive', icon: TrendingDown };
    if (percentage <= 50) return { status: 'medium', color: 'secondary', icon: Minus };
    return { status: 'high', color: 'default', icon: TrendingUp };
  };

  const getRecentUpdate = (roomId: string) => {
    return availabilityUpdates.find(update => update.roomId === roomId);
  };

  const formatLastUpdate = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just updated';
    if (diffInMinutes < 60) return `Updated ${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `Updated ${diffInHours}h ago`;
    
    return `Updated ${Math.floor(diffInHours / 24)}d ago`;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  const totalRooms = rooms.reduce((sum, room) => sum + room.totalRooms, 0);
  const totalAvailable = rooms.reduce((sum, room) => sum + room.availableRooms, 0);
  const occupancyRate = totalRooms > 0 ? ((totalRooms - totalAvailable) / totalRooms) * 100 : 0;

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bed className="w-5 h-5" />
              Real-time Availability
            </div>
            {lastUpdate && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Clock className="w-4 h-4" />
                {formatLastUpdate(lastUpdate)}
              </div>
            )}
          </CardTitle>
        </CardHeader>
      )}
      
      <CardContent className="space-y-4">
        {/* Overall Statistics */}
        <div className="grid grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold">{totalAvailable}</div>
            <div className="text-sm text-muted-foreground">Available</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{totalRooms}</div>
            <div className="text-sm text-muted-foreground">Total Rooms</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{occupancyRate.toFixed(1)}%</div>
            <div className="text-sm text-muted-foreground">Occupancy</div>
          </div>
        </div>

        {/* Room-wise Availability */}
        <div className="space-y-3">
          {rooms.map((room) => {
            const { status, color, icon: StatusIcon } = getAvailabilityStatus(
              room.availableRooms, 
              room.totalRooms
            );
            const recentUpdate = getRecentUpdate(room.id);
            const availabilityPercentage = (room.availableRooms / room.totalRooms) * 100;

            return (
              <div key={room.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{room.name}</h4>
                      <Badge variant="outline" className="capitalize text-xs">
                        {room.type}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant={color as any} className="flex items-center gap-1">
                      <StatusIcon className="w-3 h-3" />
                      {status === 'sold-out' ? 'Sold Out' : 
                       status === 'low' ? 'Low Stock' :
                       status === 'medium' ? 'Available' : 'Good Stock'}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="flex items-center gap-1">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      Capacity: {room.capacity} guests
                    </span>
                    <span className="font-medium">
                      {room.availableRooms}/{room.totalRooms} available
                    </span>
                  </div>
                  
                  <Progress 
                    value={availabilityPercentage} 
                    className="h-2"
                  />
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>₹{room.price.toLocaleString()}/night</span>
                    {recentUpdate && (
                      <span>{formatLastUpdate(recentUpdate.lastUpdated)}</span>
                    )}
                  </div>
                </div>

                {status === 'sold-out' && (
                  <div className="flex items-center gap-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                    <AlertTriangle className="w-4 h-4" />
                    This room type is currently sold out
                  </div>
                )}
                
                {status === 'low' && (
                  <div className="flex items-center gap-2 text-sm text-orange-600 bg-orange-50 p-2 rounded">
                    <TrendingDown className="w-4 h-4" />
                    Only {room.availableRooms} room{room.availableRooms !== 1 ? 's' : ''} left
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {rooms.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Bed className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No rooms available for this hotel</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RealTimeAvailability;
