/* Touch Booking Component Styles - Enhanced */

/* Hide scrollbar for webkit browsers */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Modern navbar animations */
.navbar-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Enhanced button hover effects */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

/* Floating animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse animation for notifications */
@keyframes pulse-ring {
  0% {
    transform: scale(0.33);
    opacity: 1;
  }
  80%, 100% {
    transform: scale(2.33);
    opacity: 0;
  }
}

.pulse-ring {
  animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

/* Smooth scrolling for touch devices */
.touch-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Enhanced card hover animations */
.room-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.room-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.3);
}

/* Modern glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Smooth scale animation */
@keyframes smooth-scale {
  0% { transform: scale(0.95); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.smooth-scale {
  animation: smooth-scale 0.3s ease-out;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.gradient-text {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Image overlay gradients */
.image-overlay {
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
}

/* Touch-friendly button styles */
.touch-button {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}

/* Swipe indicator animation */
@keyframes swipe-hint {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(10px); }
}

.swipe-hint {
  animation: swipe-hint 2s ease-in-out infinite;
}

/* Loading skeleton animation */
@keyframes skeleton-loading {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
  .room-card {
    width: 280px;
  }
}

@media (max-width: 640px) {
  .room-card {
    width: 260px;
  }
}

/* Custom scrollbar for desktop */
@media (min-width: 1024px) {
  .custom-scrollbar::-webkit-scrollbar {
    height: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

/* Backdrop blur support */
.backdrop-blur-fallback {
  background-color: rgba(255, 255, 255, 0.9);
}

@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur-fallback {
    background-color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
  }
}

/* Touch feedback */
.touch-feedback:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* Gradient backgrounds */
.gradient-orange {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.gradient-blue {
  background: linear-gradient(135deg, #eff6ff 0%, #bfdbfe 100%);
}

.gradient-green {
  background: linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%);
}

/* Text line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Modern button ripple effect */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::after {
  width: 300px;
  height: 300px;
}

/* Smooth page transitions */
.page-transition {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Bounce animation for interactive elements */
@keyframes bounce-subtle {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

.bounce-subtle {
  animation: bounce-subtle 2s infinite;
}
