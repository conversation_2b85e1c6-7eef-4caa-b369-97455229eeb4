// Manual test booking creation script
// Copy and paste this into browser console to create test bookings

(async function createTestBookings() {
  console.log('🚀 Creating test bookings...');
  
  try {
    // Check if user is signed in
    const currentUser = firebase.auth().currentUser;
    if (!currentUser) {
      console.error('❌ Please sign in first!');
      return;
    }
    
    console.log('✅ User signed in:', currentUser.email);
    
    // Get Firestore reference
    const db = firebase.firestore();
    
    // Create multiple test bookings
    const testBookings = [
      {
        hotelId: 'hotel-001',
        hotelName: 'Arunachala Palace Hotel',
        guestName: currentUser.displayName || 'Test User',
        guestEmail: currentUser.email,
        guestPhone: '+91 9876543210',
        checkIn: firebase.firestore.Timestamp.fromDate(new Date('2024-12-25')),
        checkOut: firebase.firestore.Timestamp.fromDate(new Date('2024-12-27')),
        rooms: [{
          roomId: 'room-001',
          roomType: 'Heritage Suite',
          quantity: 1,
          pricePerNight: 3500,
          amenities: ['WiFi', 'AC', 'TV', 'Balcony'],
          maxOccupancy: 2
        }],
        adults: 2,
        children: 0,
        nights: 2,
        totalRooms: 1,
        totalAmount: 7000,
        bookingSource: 'website',
        userId: currentUser.uid,
        createdBy: currentUser.uid,
        guestNotes: 'Temple view room preferred',
        createdAt: firebase.firestore.Timestamp.now(),
        updatedAt: firebase.firestore.Timestamp.now(),
        status: 'confirmed',
        paymentStatus: 'pending'
      },
      {
        hotelId: 'hotel-002',
        hotelName: 'Ramana Ashram Guest House',
        guestName: currentUser.displayName || 'Test User',
        guestEmail: currentUser.email,
        guestPhone: '+91 9876543210',
        checkIn: firebase.firestore.Timestamp.fromDate(new Date('2024-11-15')),
        checkOut: firebase.firestore.Timestamp.fromDate(new Date('2024-11-17')),
        rooms: [{
          roomId: 'room-002',
          roomType: 'Meditation Room',
          quantity: 1,
          pricePerNight: 1500,
          amenities: ['WiFi', 'Fan', 'Meditation Corner'],
          maxOccupancy: 1
        }],
        adults: 1,
        children: 0,
        nights: 2,
        totalRooms: 1,
        totalAmount: 3000,
        bookingSource: 'website',
        userId: currentUser.uid,
        createdBy: currentUser.uid,
        guestNotes: 'Quiet room for meditation',
        createdAt: firebase.firestore.Timestamp.now(),
        updatedAt: firebase.firestore.Timestamp.now(),
        status: 'completed',
        paymentStatus: 'paid'
      },
      {
        hotelId: 'hotel-003',
        hotelName: 'Family Paradise Resort',
        guestName: currentUser.displayName || 'Test User',
        guestEmail: currentUser.email,
        guestPhone: '+91 9876543210',
        checkIn: firebase.firestore.Timestamp.fromDate(new Date('2025-01-10')),
        checkOut: firebase.firestore.Timestamp.fromDate(new Date('2025-01-12')),
        rooms: [{
          roomId: 'room-003',
          roomType: 'Family Suite',
          quantity: 1,
          pricePerNight: 4500,
          amenities: ['WiFi', 'AC', 'TV', 'Kitchen', 'Pool Access'],
          maxOccupancy: 4
        }],
        adults: 2,
        children: 2,
        nights: 2,
        totalRooms: 1,
        totalAmount: 9000,
        bookingSource: 'website',
        userId: currentUser.uid,
        createdBy: currentUser.uid,
        guestNotes: 'Extra bed for children, pool access required',
        createdAt: firebase.firestore.Timestamp.now(),
        updatedAt: firebase.firestore.Timestamp.now(),
        status: 'pending',
        paymentStatus: 'pending'
      }
    ];
    
    console.log('📝 Creating', testBookings.length, 'test bookings...');
    
    // Create each booking
    for (let i = 0; i < testBookings.length; i++) {
      const booking = testBookings[i];
      console.log(`Creating booking ${i + 1}: ${booking.hotelName}`);
      
      try {
        const docRef = await db.collection('bookings').add(booking);
        console.log(`✅ Booking ${i + 1} created with ID:`, docRef.id);
      } catch (error) {
        console.error(`❌ Failed to create booking ${i + 1}:`, error);
      }
    }
    
    console.log('🎉 Test booking creation complete!');
    console.log('💡 Now refresh the "My Bookings" page to see your bookings');
    
    // Try to refresh the page automatically
    if (window.location.pathname.includes('bookings')) {
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    }
    
  } catch (error) {
    console.error('❌ Error creating test bookings:', error);
  }
})();

// Also make it available as a function
window.createTestBookings = async function() {
  console.log('🚀 Manual test booking creation...');
  // Same logic as above
};
