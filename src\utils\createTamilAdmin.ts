// Utility to create Tamil admin account manually
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { Admin } from '@/types/hotel';

// Function to create Tamil admin account
export const createTamilAdmin = async () => {
  const adminData = {
    email: '<EMAIL>',
    password: 'tamiladmin',
    name: 'Tamil Administrator',
    role: 'super_admin' as const,
    permissions: [
      'manage_users',
      'manage_admins',
      'manage_hotels',
      'manage_bookings',
      'view_analytics',
      'manage_settings',
      'manage_payments',
      'manage_reviews',
      'system_admin'
    ]
  };

  try {
    console.log('🚀 Creating Tamil admin account...');
    console.log('📧 Email:', adminData.email);
    console.log('🔑 Password:', adminData.password);
    
    // Create Firebase user
    const userCredential = await createUserWithEmailAndPassword(
      auth, 
      adminData.email, 
      adminData.password
    );
    const user = userCredential.user;
    console.log('✅ Firebase user created with UID:', user.uid);

    // Update Firebase profile
    await updateProfile(user, {
      displayName: adminData.name
    });
    console.log('✅ Firebase profile updated');

    // Create admin document in Firestore
    const adminDoc: Admin = {
      id: user.uid,
      name: adminData.name,
      email: adminData.email,
      role: adminData.role,
      permissions: adminData.permissions,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLogin: new Date()
    };

    await setDoc(doc(db, 'admins', user.uid), adminDoc);
    console.log('✅ Admin document created in Firestore');
    
    console.log('🎉 Tamil admin account created successfully!');
    console.log('📋 Account Details:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Password: tamiladmin');
    console.log('   - Name: Tamil Administrator');
    console.log('   - Role: super_admin');
    console.log('   - UID:', user.uid);
    console.log('');
    console.log('🔐 You can now sign in with these credentials!');
    
    return {
      success: true,
      uid: user.uid,
      email: adminData.email,
      name: adminData.name
    };
    
  } catch (error: any) {
    console.error('❌ Error creating Tamil admin account:', error);
    
    if (error.code === 'auth/email-already-in-use') {
      console.log('⚠️  Account already exists! You can try to sign in.');
      return {
        success: false,
        error: 'Account already exists',
        message: '<NAME_EMAIL> is already registered. Try signing in.'
      };
    } else if (error.code === 'auth/weak-password') {
      console.log('⚠️  Password is too weak. Firebase requires at least 6 characters.');
      return {
        success: false,
        error: 'Weak password',
        message: 'Password must be at least 6 characters long.'
      };
    } else {
      return {
        success: false,
        error: error.code || 'Unknown error',
        message: error.message || 'Failed to create admin account'
      };
    }
  }
};

// Function to check if Tamil admin exists
export const checkTamilAdmin = async () => {
  try {
    const response = await fetch(`https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=${import.meta.env.VITE_FIREBASE_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: ['<EMAIL>']
      })
    });
    
    const data = await response.json();
    
    if (data.users && data.users.length > 0) {
      console.log('✅ Tamil admin account exists in Firebase Auth');
      console.log('📧 Email:', data.users[0].email);
      console.log('👤 Display Name:', data.users[0].displayName);
      console.log('🆔 UID:', data.users[0].localId);
      return true;
    } else {
      console.log('❌ Tamil admin account does not exist in Firebase Auth');
      return false;
    }
  } catch (error) {
    console.error('Error checking Tamil admin:', error);
    return false;
  }
};

// Make functions available globally for console access
if (typeof window !== 'undefined') {
  (window as any).createTamilAdmin = createTamilAdmin;
  (window as any).checkTamilAdmin = checkTamilAdmin;
}

// Export for use in components
export default createTamilAdmin;
