// Real-time service for Firebase Realtime Database features
import {
  collection,
  doc,
  onSnapshot,
  query,
  where,
  orderBy,
  limit,
  updateDoc,
  addDoc,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Hotel, Room, Booking } from '@/types/hotel';

// Collections
const NOTIFICATIONS_COLLECTION = 'notifications';
const ROOM_AVAILABILITY_COLLECTION = 'roomAvailability';
const BOOKING_UPDATES_COLLECTION = 'bookingUpdates';

// Notification types
export interface Notification {
  id: string;
  type: 'booking_confirmed' | 'booking_cancelled' | 'room_availability' | 'admin_message';
  title: string;
  message: string;
  userId?: string;
  adminId?: string;
  hotelId?: string;
  bookingId?: string;
  isRead: boolean;
  createdAt: Date;
  expiresAt?: Date;
}

export interface RoomAvailabilityUpdate {
  id: string;
  roomId: string;
  hotelId: string;
  availableRooms: number;
  totalRooms: number;
  lastUpdated: Date;
  updatedBy: string;
}

export interface BookingUpdate {
  id: string;
  bookingId: string;
  status: Booking['status'];
  paymentStatus: Booking['paymentStatus'];
  updatedBy: string;
  updatedAt: Date;
  notes?: string;
}

// Real-time listeners
export class RealtimeService {
  private static instance: RealtimeService;
  private listeners: Map<string, () => void> = new Map();

  static getInstance(): RealtimeService {
    if (!RealtimeService.instance) {
      RealtimeService.instance = new RealtimeService();
    }
    return RealtimeService.instance;
  }

  // Listen to hotel availability updates
  subscribeToHotelAvailability(
    hotelId: string,
    callback: (rooms: RoomAvailabilityUpdate[]) => void
  ): string {
    const listenerId = `hotel_availability_${hotelId}`;
    
    const q = query(
      collection(db, ROOM_AVAILABILITY_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('lastUpdated', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const updates = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as RoomAvailabilityUpdate[];
      
      callback(updates);
    });

    this.listeners.set(listenerId, unsubscribe);
    return listenerId;
  }

  // Listen to booking updates
  subscribeToBookingUpdates(
    bookingId: string,
    callback: (updates: BookingUpdate[]) => void
  ): string {
    const listenerId = `booking_updates_${bookingId}`;
    
    const q = query(
      collection(db, BOOKING_UPDATES_COLLECTION),
      where('bookingId', '==', bookingId),
      orderBy('updatedAt', 'desc'),
      limit(10)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const updates = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as BookingUpdate[];
      
      callback(updates);
    });

    this.listeners.set(listenerId, unsubscribe);
    return listenerId;
  }

  // Listen to user notifications
  subscribeToUserNotifications(
    userId: string,
    callback: (notifications: Notification[]) => void
  ): string {
    const listenerId = `user_notifications_${userId}`;
    
    const q = query(
      collection(db, NOTIFICATIONS_COLLECTION),
      where('userId', '==', userId),
      where('isRead', '==', false),
      orderBy('createdAt', 'desc'),
      limit(20)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Notification[];
      
      callback(notifications);
    });

    this.listeners.set(listenerId, unsubscribe);
    return listenerId;
  }

  // Listen to admin notifications
  subscribeToAdminNotifications(
    adminId: string,
    callback: (notifications: Notification[]) => void
  ): string {
    const listenerId = `admin_notifications_${adminId}`;
    
    const q = query(
      collection(db, NOTIFICATIONS_COLLECTION),
      where('adminId', '==', adminId),
      where('isRead', '==', false),
      orderBy('createdAt', 'desc'),
      limit(50)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Notification[];
      
      callback(notifications);
    });

    this.listeners.set(listenerId, unsubscribe);
    return listenerId;
  }

  // Listen to all bookings for admin dashboard
  subscribeToAllBookings(
    callback: (bookings: Booking[]) => void
  ): string {
    const listenerId = 'all_bookings';
    
    const q = query(
      collection(db, 'bookings'),
      orderBy('createdAt', 'desc'),
      limit(100)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const bookings = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Booking[];
      
      callback(bookings);
    });

    this.listeners.set(listenerId, unsubscribe);
    return listenerId;
  }

  // Unsubscribe from a listener
  unsubscribe(listenerId: string): void {
    const unsubscribe = this.listeners.get(listenerId);
    if (unsubscribe) {
      unsubscribe();
      this.listeners.delete(listenerId);
    }
  }

  // Unsubscribe from all listeners
  unsubscribeAll(): void {
    this.listeners.forEach((unsubscribe) => unsubscribe());
    this.listeners.clear();
  }
}

// Utility functions for creating real-time updates
export const createNotification = async (notification: Omit<Notification, 'id' | 'createdAt'>): Promise<string> => {
  try {
    const docRef = await addDoc(collection(db, NOTIFICATIONS_COLLECTION), {
      ...notification,
      createdAt: serverTimestamp()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

export const updateRoomAvailability = async (
  roomId: string,
  hotelId: string,
  availableRooms: number,
  totalRooms: number,
  updatedBy: string
): Promise<void> => {
  try {
    await addDoc(collection(db, ROOM_AVAILABILITY_COLLECTION), {
      roomId,
      hotelId,
      availableRooms,
      totalRooms,
      lastUpdated: serverTimestamp(),
      updatedBy
    });

    // Also update the room document
    const roomRef = doc(db, 'rooms', roomId);
    await updateDoc(roomRef, {
      availableRooms,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating room availability:', error);
    throw error;
  }
};

export const createBookingUpdate = async (
  bookingId: string,
  status: Booking['status'],
  paymentStatus: Booking['paymentStatus'],
  updatedBy: string,
  notes?: string
): Promise<void> => {
  try {
    await addDoc(collection(db, BOOKING_UPDATES_COLLECTION), {
      bookingId,
      status,
      paymentStatus,
      updatedBy,
      updatedAt: serverTimestamp(),
      notes
    });

    // Also update the booking document
    const bookingRef = doc(db, 'bookings', bookingId);
    await updateDoc(bookingRef, {
      status,
      paymentStatus,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error creating booking update:', error);
    throw error;
  }
};

export const markNotificationAsRead = async (notificationId: string): Promise<void> => {
  try {
    const notificationRef = doc(db, NOTIFICATIONS_COLLECTION, notificationId);
    await updateDoc(notificationRef, {
      isRead: true
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

// Auto-create notifications for booking events
export const notifyBookingConfirmed = async (booking: Booking): Promise<void> => {
  await createNotification({
    type: 'booking_confirmed',
    title: 'Booking Confirmed',
    message: `Your booking for ${booking.guestInfo.name} has been confirmed.`,
    userId: booking.userId,
    bookingId: booking.id,
    isRead: false
  });
};

export const notifyBookingCancelled = async (booking: Booking): Promise<void> => {
  await createNotification({
    type: 'booking_cancelled',
    title: 'Booking Cancelled',
    message: `Your booking for ${booking.guestInfo.name} has been cancelled.`,
    userId: booking.userId,
    bookingId: booking.id,
    isRead: false
  });
};

export const notifyAdminNewBooking = async (booking: Booking, adminId: string): Promise<void> => {
  await createNotification({
    type: 'booking_confirmed',
    title: 'New Booking Received',
    message: `New booking from ${booking.guestInfo.name} for ${new Date(booking.checkInDate).toLocaleDateString()}.`,
    adminId,
    bookingId: booking.id,
    hotelId: booking.hotelId,
    isRead: false
  });
};

export default RealtimeService;
