// Hotel showcase component for displaying all Tiruvannamalai hotels
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Star, 
  MapPin, 
  Phone, 
  Mail, 
  Wifi, 
  Car, 
  Utensils, 
  Dumbbell,
  Waves,
  Coffee,
  Shield,
  Clock,
  Users,
  IndianRupee
} from 'lucide-react';
import { getTiruvannamalaiHotels } from '@/services/hotelService';
import { Hotel } from '@/types/hotel';
import { toast } from 'sonner';

interface HotelShowcaseProps {
  className?: string;
  onSelectHotel?: (hotelId: string) => void;
}

const HotelShowcase: React.FC<HotelShowcaseProps> = ({ className, onSelectHotel }) => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHotels();
  }, []);

  const loadHotels = async () => {
    try {
      setLoading(true);
      const hotelsData = await getTiruvannamalaiHotels();
      setHotels(hotelsData);
    } catch (error) {
      console.error('Error loading hotels:', error);
      toast.error('Failed to load hotels');
    } finally {
      setLoading(false);
    }
  };

  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi')) return <Wifi className="w-4 h-4" />;
    if (amenityLower.includes('parking')) return <Car className="w-4 h-4" />;
    if (amenityLower.includes('restaurant') || amenityLower.includes('dining')) return <Utensils className="w-4 h-4" />;
    if (amenityLower.includes('gym') || amenityLower.includes('fitness')) return <Dumbbell className="w-4 h-4" />;
    if (amenityLower.includes('pool') || amenityLower.includes('swimming')) return <Waves className="w-4 h-4" />;
    if (amenityLower.includes('coffee') || amenityLower.includes('cafe')) return <Coffee className="w-4 h-4" />;
    if (amenityLower.includes('security') || amenityLower.includes('safe')) return <Shield className="w-4 h-4" />;
    return null;
  };

  const handleSelectHotel = (hotelId: string) => {
    if (onSelectHotel) {
      onSelectHotel(hotelId);
    } else {
      // Scroll to booking section
      const bookingSection = document.getElementById('booking');
      if (bookingSection) {
        bookingSection.scrollIntoView({ behavior: 'smooth' });
        toast.success('Please complete the booking form to reserve your stay.');
      }
    }
  };

  if (loading) {
    return (
      <section className={cn('py-16 md:py-24 bg-background', className)}>
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="hotels" className={cn('py-16 md:py-24 bg-muted/30', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4">
              Premium Hotels in Tiruvannamalai
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover our carefully selected collection of hotels, each offering unique experiences 
              in the spiritual heart of Tamil Nadu
            </p>
          </div>
        </FadeIn>

        <div className="grid gap-8">
          {hotels.map((hotel, index) => (
            <FadeIn key={hotel.id} delay={index * 200}>
              <Card className="overflow-hidden hover:shadow-xl transition-all duration-500">
                <div className="grid lg:grid-cols-2 gap-0">
                  {/* Hotel Images */}
                  <div className="relative h-64 lg:h-auto">
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      {hotel.images && hotel.images.length > 0 ? (
                        <div className="relative w-full h-full">
                          <img 
                            src={hotel.images[0]} 
                            alt={hotel.name}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                          <div className="hidden flex-col items-center justify-center text-muted-foreground absolute inset-0">
                            <MapPin className="w-12 h-12 mb-2" />
                            <span className="text-sm">Hotel Image</span>
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <MapPin className="w-12 h-12 mb-2" />
                          <span className="text-sm">Hotel Image</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Rating Badge */}
                    <div className="absolute top-4 right-4">
                      <Badge className="bg-black/70 text-white">
                        <Star className="w-3 h-3 mr-1 fill-current" />
                        {hotel.rating}/5
                      </Badge>
                    </div>

                    {/* Price Range Badge */}
                    <div className="absolute bottom-4 left-4">
                      <Badge className="bg-primary text-primary-foreground">
                        <IndianRupee className="w-3 h-3 mr-1" />
                        ₹{hotel.priceRange.min.toLocaleString()} - ₹{hotel.priceRange.max.toLocaleString()}
                      </Badge>
                    </div>
                  </div>

                  {/* Hotel Information */}
                  <div className="p-6 lg:p-8">
                    <CardHeader className="p-0 mb-4">
                      <CardTitle className="text-2xl font-serif mb-2">{hotel.name}</CardTitle>
                      <p className="text-muted-foreground leading-relaxed">{hotel.description}</p>
                    </CardHeader>

                    <CardContent className="p-0 space-y-4">
                      {/* Contact Information */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                          <span className="truncate">{hotel.address}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                          <span>{hotel.phone}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                          <span className="truncate">{hotel.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                          <span>{hotel.totalReviews} reviews</span>
                        </div>
                      </div>

                      {/* Check-in/Check-out Times */}
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>Check-in: {hotel.checkInTime}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>Check-out: {hotel.checkOutTime}</span>
                        </div>
                      </div>

                      {/* Amenities */}
                      <div>
                        <h4 className="font-medium mb-2 text-sm">Hotel Amenities</h4>
                        <div className="flex flex-wrap gap-2">
                          {hotel.amenities.slice(0, 6).map((amenity, i) => (
                            <div key={i} className="flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-full">
                              {getAmenityIcon(amenity)}
                              <span>{amenity}</span>
                            </div>
                          ))}
                          {hotel.amenities.length > 6 && (
                            <Badge variant="outline" className="text-xs">
                              +{hotel.amenities.length - 6} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3 pt-4">
                        <Button 
                          className="flex-1"
                          onClick={() => handleSelectHotel(hotel.id)}
                        >
                          Book This Hotel
                        </Button>
                        {hotel.website && (
                          <Button 
                            variant="outline"
                            onClick={() => window.open(hotel.website, '_blank')}
                          >
                            Visit Website
                          </Button>
                        )}
                      </div>

                      {/* Additional Images */}
                      {hotel.images && hotel.images.length > 1 && (
                        <div className="pt-4">
                          <div className="flex gap-2 overflow-x-auto">
                            {hotel.images.slice(1, 5).map((image, index) => (
                              <img
                                key={index}
                                src={image}
                                alt={`${hotel.name} ${index + 2}`}
                                className="w-16 h-16 object-cover rounded border flex-shrink-0"
                              />
                            ))}
                            {hotel.images.length > 5 && (
                              <div className="w-16 h-16 bg-muted rounded border flex items-center justify-center text-xs text-muted-foreground flex-shrink-0">
                                +{hotel.images.length - 5}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </div>
                </div>
              </Card>
            </FadeIn>
          ))}
        </div>

        {hotels.length === 0 && (
          <FadeIn>
            <div className="text-center py-12">
              <MapPin className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-xl font-semibold mb-2">No hotels available</h3>
              <p className="text-muted-foreground">
                Please check back later or contact us directly for assistance.
              </p>
            </div>
          </FadeIn>
        )}
      </div>
    </section>
  );
};

export default HotelShowcase;
