# 🔐 Admin Setup Guide - One Touch Hotels

## 📋 Quick Setup Instructions

### 1. **Deploy Firestore Rules**
```bash
# Deploy the security rules to Firebase
firebase deploy --only firestore:rules
```

### 2. **Initialize Admin Accounts**
Open your browser console on the app and run:
```javascript
// Import the initialization script
import { initializeAllAdmins } from './src/scripts/initializeAdmins.ts';

// Run the initialization
initializeAllAdmins();
```

## 🔑 Default Admin Credentials

### **Super Administrator**
```
📧 Email: <EMAIL>
🔑 Password: Admin@123456
👤 Role: Super Admin
🛡️ Permissions: Full system access
```

### **Hotel Manager**
```
📧 Email: <EMAIL>
🔑 Password: Manager@123456
👤 Role: Hotel Admin
🛡️ Permissions: Hotel & booking management
```

### **Staff Member**
```
📧 Email: <EMAIL>
🔑 Password: Staff@123456
👤 Role: Staff
🛡️ Permissions: Booking management & reviews
```

## 🛡️ Admin Permissions Structure

### **Super Admin (`super_admin`)**
- ✅ Manage Users
- ✅ Manage Admins
- ✅ Manage Hotels
- ✅ Manage Bookings
- ✅ View Analytics
- ✅ Manage Settings
- ✅ Manage Payments
- ✅ Manage Reviews
- ✅ System Administration

### **Hotel Admin (`admin`)**
- ✅ Manage Hotels
- ✅ Manage Bookings
- ✅ View Analytics
- ✅ Manage Reviews
- ✅ Manage Rooms

### **Staff (`manager`)**
- ✅ Manage Bookings
- ✅ View Analytics
- ✅ Manage Reviews

## 🔒 Firestore Security Rules Overview

### **User Access**
- ✅ Users can read/write their own profile
- ✅ Users can read/write their own bookings
- ✅ Users can create new bookings
- ✅ Users can read/write their own notifications

### **Admin Access**
- ✅ Admins can read all user profiles
- ✅ Admins can manage bookings based on permissions
- ✅ Admins can view analytics based on permissions
- ✅ Super admins can manage all admin accounts

### **Public Access**
- ✅ Anyone can read hotel information
- ✅ Anyone can read room information
- ✅ Anyone can read public settings

## 🚀 Manual Admin Creation (Alternative Method)

If the script doesn't work, create admins manually:

### **Step 1: Create Firebase User**
1. Go to Firebase Console → Authentication → Users
2. Click "Add User"
3. Enter email and password
4. Note the User UID

### **Step 2: Create Admin Document**
1. Go to Firebase Console → Firestore Database
2. Create collection: `admins`
3. Create document with User UID as document ID
4. Add the following fields:

```json
{
  "id": "USER_UID_HERE",
  "name": "Admin Name",
  "email": "<EMAIL>",
  "role": "super_admin",
  "permissions": [
    "manage_users",
    "manage_admins", 
    "manage_hotels",
    "manage_bookings",
    "view_analytics",
    "manage_settings",
    "manage_payments",
    "manage_reviews",
    "system_admin"
  ],
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

## 🔧 Testing Admin Access

### **Test Super Admin**
1. Sign in with: `<EMAIL>` / `Admin@123456`
2. Should see admin badge in navbar
3. Should have access to admin dashboard
4. Should be able to manage all features

### **Test Hotel Manager**
1. Sign in with: `<EMAIL>` / `Manager@123456`
2. Should see admin badge in navbar
3. Should have limited admin access
4. Should be able to manage hotels and bookings

### **Test Staff**
1. Sign in with: `<EMAIL>` / `Staff@123456`
2. Should see admin badge in navbar
3. Should have basic admin access
4. Should be able to manage bookings only

## 🔐 Security Best Practices

### **Production Setup**
1. **Change Default Passwords**
   - Change all default passwords after first login
   - Use strong, unique passwords for each admin

2. **Enable 2FA**
   - Enable two-factor authentication for all admin accounts
   - Use authenticator apps for better security

3. **Regular Audits**
   - Regularly review admin permissions
   - Remove inactive admin accounts
   - Monitor admin activity logs

4. **Firestore Rules**
   - Remove the development fallback rule in production
   - Implement stricter field-level validation
   - Add rate limiting for sensitive operations

### **Environment Variables**
Create `.env.local` file with:
```env
# Firebase Admin SDK (for server-side operations)
FIREBASE_ADMIN_PROJECT_ID=your-project-id
FIREBASE_ADMIN_PRIVATE_KEY=your-private-key
FIREBASE_ADMIN_CLIENT_EMAIL=your-client-email

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin@123456
```

## 🆘 Troubleshooting

### **Admin Login Issues**
1. Check if admin document exists in Firestore
2. Verify `isActive` field is `true`
3. Check if user exists in Firebase Authentication
4. Verify Firestore rules are deployed

### **Permission Issues**
1. Check admin document permissions array
2. Verify admin role is correct
3. Check Firestore security rules
4. Clear browser cache and try again

### **Firestore Rules Issues**
1. Deploy rules: `firebase deploy --only firestore:rules`
2. Check Firebase Console for rule errors
3. Test rules in Firebase Console simulator
4. Verify helper functions are working

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Check Firebase Console for authentication/database errors
3. Verify all environment variables are set
4. Test with a fresh browser session

---

**⚠️ Important Security Note:**
Always change default passwords in production and implement proper security measures including 2FA, regular password rotation, and access auditing.
