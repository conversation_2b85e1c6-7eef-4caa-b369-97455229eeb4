// Enhanced Amenities Component with Advanced Features
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Wifi,
  Car,
  Utensils,
  Coffee,
  Dumbbell,
  Waves,
  Shield,
  Briefcase,
  MapPin,
  Phone,
  Snowflake,
  Tv,
  Bath,
  Shirt,
  Baby,
  PawPrint,
  Plane,
  Gift,
  Heart,
  Star,
  Zap,
  Sun,
  Clock,
  Eye,
  EyeOff,
  Search
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AmenitiesProps {
  className?: string;
}

// Enhanced amenity type
interface Amenity {
  id: string;
  title: string;
  description: string;
  icon: any;
  category: 'wellness' | 'dining' | 'business' | 'entertainment' | 'service' | 'comfort';
  priority: 'high' | 'medium' | 'low';
  available: boolean;
  featured: boolean;
}

const Amenities: React.FC<AmenitiesProps> = ({ className }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isAnimationPaused, setIsAnimationPaused] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showOnlyFeatured, setShowOnlyFeatured] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Comprehensive amenities data
  const amenities: Amenity[] = [
    {
      id: 'wifi',
      title: 'High-Speed WiFi',
      description: 'Complimentary high-speed internet access throughout the property',
      icon: Wifi,
      category: 'business',
      priority: 'high',
      available: true,
      featured: true
    },
    {
      id: 'parking',
      title: 'Free Parking',
      description: 'Secure parking facility available for all guests',
      icon: Car,
      category: 'service',
      priority: 'high',
      available: true,
      featured: true
    },
    {
      id: 'restaurant',
      title: 'Fine Dining Restaurant',
      description: 'Multi-cuisine restaurant serving authentic local and international dishes',
      icon: Utensils,
      category: 'dining',
      priority: 'high',
      available: true,
      featured: true
    },
    {
      id: 'coffee',
      title: 'Coffee Shop',
      description: 'Premium coffee and light snacks available 24/7',
      icon: Coffee,
      category: 'dining',
      priority: 'medium',
      available: true,
      featured: false
    },
    {
      id: 'fitness',
      title: 'Fitness Center',
      description: 'Modern gym with latest equipment and personal training',
      icon: Dumbbell,
      category: 'wellness',
      priority: 'high',
      available: true,
      featured: true
    },
    {
      id: 'pool',
      title: 'Swimming Pool',
      description: 'Rooftop infinity pool with stunning city views',
      icon: Waves,
      category: 'wellness',
      priority: 'high',
      available: true,
      featured: true
    },
    {
      id: 'security',
      title: '24/7 Security',
      description: 'Round-the-clock security with CCTV monitoring',
      icon: Shield,
      category: 'service',
      priority: 'high',
      available: true,
      featured: false
    },
    {
      id: 'business',
      title: 'Business Center',
      description: 'Fully equipped business center with meeting rooms',
      icon: Briefcase,
      category: 'business',
      priority: 'medium',
      available: true,
      featured: false
    },
    {
      id: 'location',
      title: 'Prime Location',
      description: 'Located in the heart of the city near major attractions',
      icon: MapPin,
      category: 'service',
      priority: 'high',
      available: true,
      featured: true
    },
    {
      id: 'room-service',
      title: '24/7 Room Service',
      description: 'Round-the-clock room service with extensive menu',
      icon: Phone,
      category: 'service',
      priority: 'medium',
      available: true,
      featured: false
    },
    {
      id: 'climate',
      title: 'Climate Control',
      description: 'Individual climate control in all rooms',
      icon: Snowflake,
      category: 'comfort',
      priority: 'high',
      available: true,
      featured: false
    },
    {
      id: 'entertainment',
      title: 'Smart Entertainment',
      description: 'Smart TV with streaming services and premium channels',
      icon: Tv,
      category: 'entertainment',
      priority: 'medium',
      available: true,
      featured: false
    },
    {
      id: 'spa',
      title: 'Luxury Spa',
      description: 'Full-service spa with traditional and modern treatments',
      icon: Bath,
      category: 'wellness',
      priority: 'high',
      available: true,
      featured: true
    },
    {
      id: 'laundry',
      title: 'Laundry Service',
      description: 'Professional laundry and dry cleaning services',
      icon: Shirt,
      category: 'service',
      priority: 'low',
      available: true,
      featured: false
    },
    {
      id: 'family',
      title: 'Family Services',
      description: 'Child-friendly amenities and babysitting services',
      icon: Baby,
      category: 'service',
      priority: 'medium',
      available: true,
      featured: false
    },
    {
      id: 'pet',
      title: 'Pet-Friendly',
      description: 'Welcome your furry friends with special pet amenities',
      icon: PawPrint,
      category: 'service',
      priority: 'low',
      available: true,
      featured: false
    },
    {
      id: 'airport',
      title: 'Airport Transfer',
      description: 'Complimentary airport pickup and drop-off service',
      icon: Plane,
      category: 'service',
      priority: 'medium',
      available: true,
      featured: true
    },
    {
      id: 'concierge',
      title: 'Concierge Services',
      description: 'Personal concierge for bookings and recommendations',
      icon: Gift,
      category: 'service',
      priority: 'high',
      available: true,
      featured: false
    },
    {
      id: 'wellness',
      title: 'Wellness Programs',
      description: 'Yoga, meditation, and wellness programs',
      icon: Heart,
      category: 'wellness',
      priority: 'medium',
      available: true,
      featured: false
    },
    {
      id: 'vip',
      title: 'VIP Lounge',
      description: 'Exclusive lounge access for premium guests',
      icon: Star,
      category: 'entertainment',
      priority: 'high',
      available: true,
      featured: true
    },
    {
      id: 'checkin',
      title: 'Fast Check-in',
      description: 'Express check-in and check-out services',
      icon: Zap,
      category: 'business',
      priority: 'medium',
      available: true,
      featured: false
    },
    {
      id: 'terrace',
      title: 'Rooftop Terrace',
      description: 'Beautiful rooftop terrace with panoramic views',
      icon: Sun,
      category: 'entertainment',
      priority: 'high',
      available: true,
      featured: true
    }
  ];

  // Category filters
  const categories = [
    { id: 'all', label: 'All Amenities', icon: Star, count: amenities.length },
    { id: 'wellness', label: 'Wellness', icon: Heart, count: amenities.filter(a => a.category === 'wellness').length },
    { id: 'dining', label: 'Dining', icon: Utensils, count: amenities.filter(a => a.category === 'dining').length },
    { id: 'business', label: 'Business', icon: Briefcase, count: amenities.filter(a => a.category === 'business').length },
    { id: 'entertainment', label: 'Entertainment', icon: Tv, count: amenities.filter(a => a.category === 'entertainment').length },
    { id: 'service', label: 'Services', icon: Gift, count: amenities.filter(a => a.category === 'service').length },
    { id: 'comfort', label: 'Comfort', icon: Snowflake, count: amenities.filter(a => a.category === 'comfort').length }
  ];

  // Filter amenities based on selected category and search
  const filteredAmenities = useCallback(() => {
    let filtered = amenities;

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(amenity => amenity.category === selectedCategory);
    }

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(amenity =>
        amenity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        amenity.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Featured filter
    if (showOnlyFeatured) {
      filtered = filtered.filter(amenity => amenity.featured);
    }

    return filtered;
  }, [selectedCategory, searchQuery, showOnlyFeatured]);

  // Auto-scroll animation
  useEffect(() => {
    if (isAnimationPaused) return;

    const scrollContainer = scrollRef.current;
    if (!scrollContainer) return;

    const scrollWidth = scrollContainer.scrollWidth;
    const clientWidth = scrollContainer.clientWidth;
    const maxScroll = scrollWidth - clientWidth;

    if (maxScroll <= 0) return;

    let scrollPosition = 0;
    const scrollSpeed = 1; // pixels per frame
    const pauseDuration = 2000; // pause at end in ms

    const animate = () => {
      if (isAnimationPaused) return;

      scrollPosition += scrollSpeed;

      if (scrollPosition >= maxScroll) {
        scrollPosition = 0;
        setTimeout(() => {
          if (!isAnimationPaused && scrollContainer) {
            scrollContainer.scrollLeft = 0;
          }
        }, pauseDuration);
      } else {
        scrollContainer.scrollLeft = scrollPosition;
      }

      requestAnimationFrame(animate);
    };

    const animationId = requestAnimationFrame(animate);

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, [isAnimationPaused, filteredAmenities()]);

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500 bg-red-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-green-500 bg-green-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const filtered = filteredAmenities();

  return (
    <section id="amenities" className={cn("py-16 bg-gradient-to-br from-gray-50 to-blue-50", className)}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Premium Amenities
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience luxury and comfort with our world-class amenities designed for your perfect stay
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="max-w-md mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search amenities..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap justify-center gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="flex items-center gap-2"
              >
                <category.icon className="w-4 h-4" />
                {category.label}
                <Badge variant="secondary" className="ml-1">
                  {category.count}
                </Badge>
              </Button>
            ))}
          </div>

          {/* Additional Filters */}
          <div className="flex justify-center gap-4">
            <Button
              variant={showOnlyFeatured ? "default" : "outline"}
              size="sm"
              onClick={() => setShowOnlyFeatured(!showOnlyFeatured)}
              className="flex items-center gap-2"
            >
              <Star className="w-4 h-4" />
              Featured Only
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAnimationPaused(!isAnimationPaused)}
              className="flex items-center gap-2"
            >
              {isAnimationPaused ? (
                <>
                  <Eye className="w-4 h-4" />
                  Resume Animation
                </>
              ) : (
                <>
                  <EyeOff className="w-4 h-4" />
                  Pause Animation
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-8 mb-12">
          <div className="text-center mb-6">
            <h3 className="text-2xl font-serif font-medium text-gray-900 mb-2">
              Luxury by the Numbers
            </h3>
            <p className="text-gray-600">
              Discover what makes our amenities world-class
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center group">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-primary/20 transition-colors duration-300">
                <Star className="w-8 h-8 text-primary" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-1 group-hover:text-primary transition-colors duration-300">
                {filtered.length}+
              </div>
              <div className="text-sm text-gray-600">Premium Amenities</div>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors duration-300">
                <Clock className="w-8 h-8 text-green-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-1 group-hover:text-green-600 transition-colors duration-300">
                24/7
              </div>
              <div className="text-sm text-gray-600">Available Services</div>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors duration-300">
                <Heart className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">
                100%
              </div>
              <div className="text-sm text-gray-600">Guest Satisfaction</div>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors duration-300">
                <Zap className="w-8 h-8 text-purple-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-1 group-hover:text-purple-600 transition-colors duration-300">
                5★
              </div>
              <div className="text-sm text-gray-600">Luxury Rating</div>
            </div>
          </div>
        </div>

        {/* Amenities Carousel */}
        <div className="relative">
          <div
            ref={scrollRef}
            className="flex gap-6 overflow-x-auto scrollbar-hide pb-4"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none'
            }}
            onMouseEnter={() => setIsAnimationPaused(true)}
            onMouseLeave={() => setIsAnimationPaused(false)}
          >
            {/* Amenities Cards */}
            {filtered.map((amenity, index) => (
              <div key={`${amenity.id}-${index}`} className="flex-shrink-0 w-80">
                <Card className={cn(
                  "text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-xl h-full",
                  getPriorityColor(amenity.priority),
                  amenity.featured && "ring-2 ring-primary/20"
                )}>
                  <CardHeader className="pb-4">
                    <div className="mx-auto mb-4 p-4 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full w-fit">
                      <amenity.icon className="w-8 h-8 text-primary" />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-900 flex items-center justify-center gap-2">
                      {amenity.title}
                      {amenity.featured && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0 px-6 pb-6">
                    <p className="text-sm text-gray-600 leading-relaxed mb-4">
                      {amenity.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <Badge
                        variant="secondary"
                        className="text-xs capitalize"
                      >
                        {amenity.category}
                      </Badge>
                      <Badge
                        variant={amenity.available ? "default" : "destructive"}
                        className="text-xs"
                      >
                        {amenity.available ? "Available" : "Coming Soon"}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}

            {/* Duplicate set for seamless loop */}
            {filtered.map((amenity, index) => (
              <div key={`duplicate-${amenity.id}-${index}`} className="flex-shrink-0 w-80">
                <Card className={cn(
                  "text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-xl h-full",
                  getPriorityColor(amenity.priority),
                  amenity.featured && "ring-2 ring-primary/20"
                )}>
                  <CardHeader className="pb-4">
                    <div className="mx-auto mb-4 p-4 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full w-fit">
                      <amenity.icon className="w-8 h-8 text-primary" />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-900 flex items-center justify-center gap-2">
                      {amenity.title}
                      {amenity.featured && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0 px-6 pb-6">
                    <p className="text-sm text-gray-600 leading-relaxed mb-4">
                      {amenity.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <Badge
                        variant="secondary"
                        className="text-xs capitalize"
                      >
                        {amenity.category}
                      </Badge>
                      <Badge
                        variant={amenity.available ? "default" : "destructive"}
                        className="text-xs"
                      >
                        {amenity.available ? "Available" : "Coming Soon"}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>

        {/* Results Info */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-600">
            Showing {filtered.length} of {amenities.length} amenities
            {selectedCategory !== 'all' && ` in ${selectedCategory}`}
            {searchQuery && ` matching "${searchQuery}"`}
          </p>
        </div>
      </div>
    </section>
  );
};

export default Amenities;