/* Enhanced Navbar Styles with Touch Support and Scrolling Features */

/* Scrollbar hiding for navigation */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Enhanced button hover and active states for touch */
.touch-manipulation:active {
  transform: scale(0.95);
  transition: transform 0.1s ease-in-out;
}

/* Smooth scrolling for navigation container */
.nav-scroll-container {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Mobile menu overlay improvements */
.mobile-menu-overlay {
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;
}

/* Prevent body scroll when mobile menu is open */
.mobile-menu-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* Enhanced backdrop blur support */
.backdrop-blur-enhanced {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Improved focus states for accessibility */
.nav-button:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 8px;
}

/* Smooth transitions for all interactive elements */
.nav-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced shadow effects */
.nav-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.nav-shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Mobile-first responsive design improvements */
@media (max-width: 768px) {
  .mobile-nav-item {
    min-height: 56px; /* Minimum touch target size */
    padding: 16px;
  }
  
  .mobile-menu-button {
    min-width: 48px;
    min-height: 48px;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .nav-scroll-container {
    max-width: 600px;
  }
}

/* Desktop enhancements */
@media (min-width: 1025px) {
  .nav-scroll-container {
    max-width: 800px;
  }
  
  .nav-button:hover {
    transform: translateY(-1px);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .nav-button {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .nav-transition,
  .touch-manipulation,
  .nav-button {
    transition: none !important;
    animation: none !important;
  }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
  .nav-glass {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* Scroll indicator for navigation */
.nav-scroll-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary)) 50%, transparent 50%);
  transition: width 0.3s ease;
}

/* Loading states */
.nav-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Error states */
.nav-error {
  border-color: hsl(var(--destructive));
  background-color: hsl(var(--destructive) / 0.1);
}

/* Success states */
.nav-success {
  border-color: hsl(var(--success));
  background-color: hsl(var(--success) / 0.1);
}

/* Animation keyframes */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Mobile menu animations */
.mobile-menu-enter {
  animation: slideInFromRight 0.3s ease-out;
}

.mobile-menu-exit {
  animation: slideOutToRight 0.3s ease-in;
}

/* Scroll to top button animations */
.scroll-top-enter {
  animation: fadeInUp 0.3s ease-out;
}

/* Haptic feedback simulation for non-supporting devices */
.haptic-feedback:active {
  animation: hapticPulse 0.1s ease-in-out;
}

@keyframes hapticPulse {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}
