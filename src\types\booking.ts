// Booking types and interfaces for Firestore
import { Timestamp } from 'firebase/firestore';

// Booking status enum
export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CHECKED_IN = 'checked_in',
  CHECKED_OUT = 'checked_out',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show'
}

// Payment status enum
export enum PaymentStatus {
  PENDING = 'pending',
  PARTIAL = 'partial',
  PAID = 'paid',
  REFUNDED = 'refunded',
  FAILED = 'failed'
}

// Room type interface
export interface BookingRoom {
  roomId: string;
  roomType: string;
  roomNumber?: string;
  quantity: number;
  pricePerNight: number;
  totalPrice: number;
  amenities: string[];
  maxOccupancy: number;
}

// Guest information interface
export interface GuestInfo {
  name: string;
  email: string;
  phone: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  zipCode?: string;
  idType?: string;
  idNumber?: string;
  dateOfBirth?: string;
  nationality?: string;
}

// Payment details interface
export interface PaymentDetails {
  method: 'card' | 'upi' | 'netbanking' | 'wallet' | 'cash' | 'bank_transfer';
  transactionId?: string;
  paymentGateway?: string;
  amount: number;
  currency: string;
  paidAt?: Timestamp;
  refundAmount?: number;
  refundedAt?: Timestamp;
  paymentReference?: string;
}

// Special requests interface
export interface SpecialRequests {
  earlyCheckIn?: boolean;
  lateCheckOut?: boolean;
  airportPickup?: boolean;
  extraBed?: boolean;
  wheelchairAccess?: boolean;
  dietaryRequirements?: string;
  roomPreference?: string;
  otherRequests?: string;
}

// Booking cancellation details
export interface CancellationDetails {
  cancelledAt: Timestamp;
  cancelledBy: string; // user ID or admin ID
  reason: string;
  refundAmount?: number;
  refundStatus?: 'pending' | 'processed' | 'failed';
  refundProcessedAt?: Timestamp;
}

// Main booking interface
export interface Booking {
  // Basic booking information
  id: string;
  bookingReference?: string; // Human-readable booking reference
  
  // User and hotel information
  userId?: string; // Optional for guest bookings
  createdBy?: string; // User ID who created the booking
  hotelId: string;
  hotelName: string;
  
  // Guest information
  guestName: string;
  guestEmail: string;
  guestPhone: string;
  guestInfo?: GuestInfo;
  
  // Booking dates
  checkIn: Timestamp;
  checkOut: Timestamp;
  nights: number;
  
  // Room information
  rooms: BookingRoom[];
  totalRooms: number;
  totalGuests: number;
  adults: number;
  children: number;
  
  // Pricing
  subtotal: number;
  taxes: number;
  fees: number;
  discount?: number;
  totalAmount: number;
  currency: string;
  
  // Status
  status: BookingStatus;
  paymentStatus: PaymentStatus;
  
  // Payment information
  paymentDetails?: PaymentDetails;
  
  // Special requests and notes
  specialRequests?: SpecialRequests;
  guestNotes?: string;
  adminNotes?: string;
  
  // Cancellation information
  cancellationDetails?: CancellationDetails;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  confirmedAt?: Timestamp;
  checkedInAt?: Timestamp;
  checkedOutAt?: Timestamp;
  
  // Source information
  bookingSource: 'website' | 'mobile' | 'phone' | 'walk_in' | 'admin';
  deviceInfo?: string;
  ipAddress?: string;
  
  // Communication
  emailSent?: boolean;
  smsSent?: boolean;
  confirmationEmailSent?: boolean;
  reminderEmailSent?: boolean;
  
  // Additional metadata
  metadata?: Record<string, any>;
}

// Booking statistics interface
export interface BookingStats {
  totalBookings: number;
  pendingBookings: number;
  confirmedBookings: number;
  cancelledBookings: number;
  checkedInBookings: number;
  checkedOutBookings: number;
  noShowBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
  occupancyRate: number;
  cancellationRate: number;
  lastUpdated: Timestamp;
}

// Hotel booking summary for quick queries
export interface HotelBookingSummary {
  bookingId: string;
  hotelId: string;
  userId?: string;
  guestName: string;
  checkIn: Timestamp;
  checkOut: Timestamp;
  status: BookingStatus;
  totalAmount: number;
  totalRooms: number;
  createdAt: Timestamp;
}

// Booking search filters
export interface BookingSearchFilters {
  status?: BookingStatus;
  paymentStatus?: PaymentStatus;
  hotelId?: string;
  userId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  checkInFrom?: Date;
  checkInTo?: Date;
  checkOutFrom?: Date;
  checkOutTo?: Date;
  minAmount?: number;
  maxAmount?: number;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  bookingSource?: string;
}

// Booking creation request
export interface CreateBookingRequest {
  hotelId: string;
  hotelName: string;
  guestName: string;
  guestEmail: string;
  guestPhone: string;
  guestInfo?: Partial<GuestInfo>;
  checkIn: Date;
  checkOut: Date;
  rooms: Omit<BookingRoom, 'totalPrice'>[];
  adults: number;
  children: number;
  specialRequests?: SpecialRequests;
  guestNotes?: string;
  bookingSource: 'website' | 'mobile' | 'phone' | 'walk_in' | 'admin';
  userId?: string;
  deviceInfo?: string;
  ipAddress?: string;
}

// Booking update request
export interface UpdateBookingRequest {
  status?: BookingStatus;
  paymentStatus?: PaymentStatus;
  adminNotes?: string;
  guestNotes?: string;
  specialRequests?: SpecialRequests;
  paymentDetails?: PaymentDetails;
}

// Booking validation result
export interface BookingValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  availableRooms?: BookingRoom[];
  alternativeDates?: Date[];
}

// Room availability check
export interface RoomAvailabilityCheck {
  hotelId: string;
  checkIn: Date;
  checkOut: Date;
  roomTypes: string[];
  guests: number;
}

// Room availability result
export interface RoomAvailabilityResult {
  available: boolean;
  availableRooms: BookingRoom[];
  unavailableRooms: string[];
  alternativeDates: Date[];
  priceRange: {
    min: number;
    max: number;
  };
}

// Booking confirmation data
export interface BookingConfirmation {
  booking: Booking;
  confirmationNumber: string;
  qrCode?: string;
  checkInInstructions: string;
  hotelContact: {
    phone: string;
    email: string;
    address: string;
  };
  cancellationPolicy: string;
  amenities: string[];
}

// Booking analytics data
export interface BookingAnalytics {
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  startDate: Date;
  endDate: Date;
  totalBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
  occupancyRate: number;
  cancellationRate: number;
  topRoomTypes: Array<{
    roomType: string;
    bookings: number;
    revenue: number;
  }>;
  bookingsBySource: Array<{
    source: string;
    bookings: number;
    percentage: number;
  }>;
  dailyStats: Array<{
    date: Date;
    bookings: number;
    revenue: number;
    occupancy: number;
  }>;
}
