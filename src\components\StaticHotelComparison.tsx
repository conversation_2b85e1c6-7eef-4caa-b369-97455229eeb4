// Static hotel comparison component that works without Firebase
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import FadeIn from './animations/FadeIn';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Star, 
  MapPin, 
  IndianRupee, 
  Users, 
  Bed,
  Wifi,
  Car,
  Utensils,
  Dumbbell,
  Waves,
  Coffee,
  Shield,
  Check,
  X,
  BarChart3
} from 'lucide-react';
import { toast } from 'sonner';

interface ComparisonHotel {
  id: string;
  name: string;
  rating: number;
  reviews: number;
  priceRange: { min: number; max: number };
  distanceFromTemple: string;
  image: string;
  amenities: string[];
  roomCount: number;
  checkInTime: string;
  checkOutTime: string;
  cancellationPolicy: string;
  highlights: string[];
}

interface StaticHotelComparisonProps {
  className?: string;
  onBookHotel?: (hotelId: string) => void;
}

const StaticHotelComparison: React.FC<StaticHotelComparisonProps> = ({ className, onBookHotel }) => {
  const [selectedHotels, setSelectedHotels] = useState<string[]>(['hotel-1', 'hotel-2', 'hotel-4']);

  const comparisonHotels: ComparisonHotel[] = [
    {
      id: 'hotel-1',
      name: 'Arunachala Palace Hotel',
      rating: 4.8,
      reviews: 324,
      priceRange: { min: 4500, max: 12000 },
      distanceFromTemple: '0.2 km',
      image: '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
      amenities: ['Free WiFi', 'Restaurant', 'Parking', 'AC', 'Room Service', 'Temple View', 'Spa', 'Gym'],
      roomCount: 45,
      checkInTime: '2:00 PM',
      checkOutTime: '12:00 PM',
      cancellationPolicy: 'Free cancellation up to 24 hours',
      highlights: ['Heritage Architecture', 'Temple View', 'Luxury Amenities']
    },
    {
      id: 'hotel-2',
      name: 'Ramana Ashram Guest House',
      rating: 4.6,
      reviews: 189,
      priceRange: { min: 2800, max: 6500 },
      distanceFromTemple: '1.5 km',
      image: '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png',
      amenities: ['Free WiFi', 'Meditation Hall', 'Garden', 'Library', 'Organic Food', 'Yoga Classes'],
      roomCount: 25,
      checkInTime: '1:00 PM',
      checkOutTime: '11:00 AM',
      cancellationPolicy: 'Free cancellation up to 48 hours',
      highlights: ['Spiritual Environment', 'Meditation Facilities', 'Organic Meals']
    },
    {
      id: 'hotel-3',
      name: 'Tiruvannamalai Business Hotel',
      rating: 4.4,
      reviews: 267,
      priceRange: { min: 3500, max: 8500 },
      distanceFromTemple: '0.8 km',
      image: '/lovable-uploads/47f9a1d0-4458-400a-8fc0-79adf093cf18.png',
      amenities: ['Free WiFi', 'Business Center', 'Conference Room', 'Gym', 'Restaurant', 'Parking'],
      roomCount: 60,
      checkInTime: '3:00 PM',
      checkOutTime: '12:00 PM',
      cancellationPolicy: 'Free cancellation up to 6 hours',
      highlights: ['Business Facilities', 'Conference Rooms', 'Corporate Services']
    },
    {
      id: 'hotel-4',
      name: 'Family Paradise Resort',
      rating: 4.7,
      reviews: 412,
      priceRange: { min: 4200, max: 9800 },
      distanceFromTemple: '2.1 km',
      image: '/lovable-uploads/34a58283-8b82-48f9-88f4-2c88b069921d.png',
      amenities: ['Swimming Pool', 'Kids Play Area', 'Restaurant', 'Parking', 'Garden', 'Game Room'],
      roomCount: 80,
      checkInTime: '2:00 PM',
      checkOutTime: '11:00 AM',
      cancellationPolicy: 'Free cancellation up to 24 hours',
      highlights: ['Family Friendly', 'Swimming Pool', 'Kids Activities']
    },
    {
      id: 'hotel-5',
      name: 'Pilgrim\'s Rest Lodge',
      rating: 4.2,
      reviews: 156,
      priceRange: { min: 1800, max: 4200 },
      distanceFromTemple: '0.1 km',
      image: '/lovable-uploads/dabbf929-5dd0-4794-a011-fe43bf4b3418.png',
      amenities: ['Free WiFi', 'AC', 'Hot Water', 'Temple Shuttle', 'Basic Restaurant'],
      roomCount: 30,
      checkInTime: '1:00 PM',
      checkOutTime: '10:00 AM',
      cancellationPolicy: 'Free cancellation up to 12 hours',
      highlights: ['Budget Friendly', 'Temple Proximity', 'Shuttle Service']
    }
  ];

  const allAmenities = Array.from(new Set(comparisonHotels.flatMap(hotel => hotel.amenities)));

  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi')) return <Wifi className="w-4 h-4" />;
    if (amenityLower.includes('restaurant') || amenityLower.includes('food')) return <Utensils className="w-4 h-4" />;
    if (amenityLower.includes('parking') || amenityLower.includes('car')) return <Car className="w-4 h-4" />;
    if (amenityLower.includes('gym') || amenityLower.includes('fitness')) return <Dumbbell className="w-4 h-4" />;
    if (amenityLower.includes('pool') || amenityLower.includes('swimming')) return <Waves className="w-4 h-4" />;
    if (amenityLower.includes('coffee') || amenityLower.includes('cafe')) return <Coffee className="w-4 h-4" />;
    if (amenityLower.includes('security') || amenityLower.includes('safe')) return <Shield className="w-4 h-4" />;
    return <Star className="w-4 h-4" />;
  };

  const toggleHotelSelection = (hotelId: string) => {
    setSelectedHotels(prev => {
      if (prev.includes(hotelId)) {
        if (prev.length <= 2) {
          toast.error('Please select at least 2 hotels to compare');
          return prev;
        }
        return prev.filter(id => id !== hotelId);
      } else {
        if (prev.length >= 3) {
          toast.error('Maximum 3 hotels can be compared at once. Please remove one to add another.');
          return prev;
        }
        return [...prev, hotelId];
      }
    });
  };

  const handleBookHotel = (hotelId: string) => {
    if (onBookHotel) {
      onBookHotel(hotelId);
    }
    const hotel = comparisonHotels.find(h => h.id === hotelId);
    if (hotel) {
      toast.success(`Selected ${hotel.name}. Scroll down to complete booking.`);
      // Scroll to booking section
      const bookingSection = document.getElementById('booking');
      if (bookingSection) {
        bookingSection.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  const selectedHotelData = comparisonHotels.filter(hotel => selectedHotels.includes(hotel.id));

  return (
    <section id="comparison" className={cn('py-16 md:py-24 bg-gradient-to-br from-green-50 to-emerald-50', className)}>
      <div className="container mx-auto px-4 md:px-6">
        <FadeIn>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-medium mb-4 flex items-center justify-center gap-3">
              <BarChart3 className="w-8 h-8 text-primary" />
              Compare Hotels
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Compare features, amenities, and prices of our top hotels to make the best choice for your stay.
            </p>
          </div>
        </FadeIn>

        {/* Hotel Selection */}
        <FadeIn delay={100}>
          <div className="mb-8">
            <h3 className="text-xl font-semibold mb-4 text-center">Select Hotels to Compare (2-3 hotels)</h3>
            <div className="flex flex-wrap justify-center gap-3">
              {comparisonHotels.map((hotel) => (
                <Button
                  key={hotel.id}
                  variant={selectedHotels.includes(hotel.id) ? "default" : "outline"}
                  onClick={() => toggleHotelSelection(hotel.id)}
                  className={cn(
                    "flex items-center gap-2",
                    selectedHotels.includes(hotel.id) && "bg-primary text-white"
                  )}
                >
                  <Star className="w-4 h-4" />
                  {hotel.name}
                </Button>
              ))}
            </div>
          </div>
        </FadeIn>

        {/* Comparison Table */}
        <FadeIn delay={200}>
          <Card className="overflow-hidden">
            <CardHeader>
              <CardTitle className="text-center">Hotel Comparison</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="text-left p-4 font-semibold">Features</th>
                      {selectedHotelData.map((hotel) => (
                        <th key={hotel.id} className="text-center p-4 min-w-[250px]">
                          <div className="space-y-2">
                            <img 
                              src={hotel.image} 
                              alt={hotel.name}
                              className="w-full h-32 object-cover rounded-lg"
                            />
                            <h4 className="font-semibold text-sm">{hotel.name}</h4>
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {/* Rating */}
                    <tr className="border-b">
                      <td className="p-4 font-medium">Rating</td>
                      {selectedHotelData.map((hotel) => (
                        <td key={hotel.id} className="p-4 text-center">
                          <div className="flex items-center justify-center gap-1">
                            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-semibold">{hotel.rating}</span>
                            <span className="text-sm text-muted-foreground">({hotel.reviews})</span>
                          </div>
                        </td>
                      ))}
                    </tr>

                    {/* Price Range */}
                    <tr className="border-b">
                      <td className="p-4 font-medium">Price Range</td>
                      {selectedHotelData.map((hotel) => (
                        <td key={hotel.id} className="p-4 text-center">
                          <div className="flex items-center justify-center gap-1">
                            <IndianRupee className="w-4 h-4" />
                            <span className="font-semibold">
                              {hotel.priceRange.min.toLocaleString()} - {hotel.priceRange.max.toLocaleString()}
                            </span>
                          </div>
                        </td>
                      ))}
                    </tr>

                    {/* Distance from Temple */}
                    <tr className="border-b">
                      <td className="p-4 font-medium">Distance from Temple</td>
                      {selectedHotelData.map((hotel) => (
                        <td key={hotel.id} className="p-4 text-center">
                          <div className="flex items-center justify-center gap-1">
                            <MapPin className="w-4 h-4" />
                            <span>{hotel.distanceFromTemple}</span>
                          </div>
                        </td>
                      ))}
                    </tr>

                    {/* Room Count */}
                    <tr className="border-b">
                      <td className="p-4 font-medium">Total Rooms</td>
                      {selectedHotelData.map((hotel) => (
                        <td key={hotel.id} className="p-4 text-center">
                          <div className="flex items-center justify-center gap-1">
                            <Bed className="w-4 h-4" />
                            <span>{hotel.roomCount} rooms</span>
                          </div>
                        </td>
                      ))}
                    </tr>

                    {/* Check-in/out Times */}
                    <tr className="border-b">
                      <td className="p-4 font-medium">Check-in / Check-out</td>
                      {selectedHotelData.map((hotel) => (
                        <td key={hotel.id} className="p-4 text-center">
                          <div className="text-sm">
                            <div>In: {hotel.checkInTime}</div>
                            <div>Out: {hotel.checkOutTime}</div>
                          </div>
                        </td>
                      ))}
                    </tr>

                    {/* Cancellation Policy */}
                    <tr className="border-b">
                      <td className="p-4 font-medium">Cancellation</td>
                      {selectedHotelData.map((hotel) => (
                        <td key={hotel.id} className="p-4 text-center">
                          <span className="text-sm">{hotel.cancellationPolicy}</span>
                        </td>
                      ))}
                    </tr>

                    {/* Amenities */}
                    {allAmenities.slice(0, 8).map((amenity) => (
                      <tr key={amenity} className="border-b">
                        <td className="p-4 font-medium">
                          <div className="flex items-center gap-2">
                            {getAmenityIcon(amenity)}
                            {amenity}
                          </div>
                        </td>
                        {selectedHotelData.map((hotel) => (
                          <td key={hotel.id} className="p-4 text-center">
                            {hotel.amenities.includes(amenity) ? (
                              <Check className="w-5 h-5 text-green-500 mx-auto" />
                            ) : (
                              <X className="w-5 h-5 text-red-500 mx-auto" />
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}

                    {/* Highlights */}
                    <tr className="border-b">
                      <td className="p-4 font-medium">Key Highlights</td>
                      {selectedHotelData.map((hotel) => (
                        <td key={hotel.id} className="p-4">
                          <div className="space-y-1">
                            {hotel.highlights.map((highlight, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs block">
                                {highlight}
                              </Badge>
                            ))}
                          </div>
                        </td>
                      ))}
                    </tr>

                    {/* Action Buttons */}
                    <tr>
                      <td className="p-4 font-medium">Book Now</td>
                      {selectedHotelData.map((hotel) => (
                        <td key={hotel.id} className="p-4 text-center">
                          <Button 
                            className="btn-primary w-full"
                            onClick={() => handleBookHotel(hotel.id)}
                          >
                            <IndianRupee className="w-4 h-4 mr-2" />
                            Book Now
                          </Button>
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </FadeIn>

        {/* Bottom CTA */}
        <FadeIn delay={400}>
          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              Need help deciding? Our travel experts are here to assist you.
            </p>
            <Button className="btn-primary">
              <Users className="w-4 h-4 mr-2" />
              Contact Our Experts
            </Button>
          </div>
        </FadeIn>
      </div>
    </section>
  );
};

export default StaticHotelComparison;
