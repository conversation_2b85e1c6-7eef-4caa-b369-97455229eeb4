// Enhanced Password Reset Service with Email Functionality
import { 
  sendPasswordResetEmail, 
  confirmPasswordReset, 
  verifyPasswordResetCode,
  checkActionCode,
  applyActionCode
} from 'firebase/auth';
import { auth } from '@/lib/firebase';

export interface PasswordResetResult {
  success: boolean;
  message: string;
  error?: string;
}

export interface PasswordResetEmailOptions {
  url?: string;
  handleCodeInApp?: boolean;
}

/**
 * Send password reset email to user
 */
export const sendPasswordResetEmailService = async (
  email: string,
  options?: PasswordResetEmailOptions
): Promise<PasswordResetResult> => {
  try {
    const actionCodeSettings = {
      url: options?.url || `${window.location.origin}/signin`,
      handleCodeInApp: options?.handleCodeInApp || false,
    };

    await sendPasswordResetEmail(auth, email, actionCodeSettings);
    
    return {
      success: true,
      message: 'Password reset email sent successfully'
    };
  } catch (error: any) {
    console.error('Password reset email error:', error);
    
    let errorMessage = 'Failed to send password reset email';
    
    switch (error.code) {
      case 'auth/user-not-found':
        errorMessage = 'No account found with this email address';
        break;
      case 'auth/invalid-email':
        errorMessage = 'Invalid email address format';
        break;
      case 'auth/too-many-requests':
        errorMessage = 'Too many requests. Please try again later';
        break;
      case 'auth/network-request-failed':
        errorMessage = 'Network error. Please check your connection';
        break;
      default:
        errorMessage = error.message || 'Failed to send password reset email';
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Verify password reset code
 */
export const verifyPasswordResetCodeService = async (
  code: string
): Promise<PasswordResetResult & { email?: string }> => {
  try {
    const email = await verifyPasswordResetCode(auth, code);
    
    return {
      success: true,
      message: 'Password reset code is valid',
      email
    };
  } catch (error: any) {
    console.error('Password reset code verification error:', error);
    
    let errorMessage = 'Invalid or expired reset code';
    
    switch (error.code) {
      case 'auth/expired-action-code':
        errorMessage = 'Password reset link has expired. Please request a new one';
        break;
      case 'auth/invalid-action-code':
        errorMessage = 'Invalid password reset link. Please request a new one';
        break;
      case 'auth/user-disabled':
        errorMessage = 'This account has been disabled';
        break;
      case 'auth/user-not-found':
        errorMessage = 'No account found for this reset request';
        break;
      default:
        errorMessage = error.message || 'Invalid or expired reset code';
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Confirm password reset with new password
 */
export const confirmPasswordResetService = async (
  code: string,
  newPassword: string
): Promise<PasswordResetResult> => {
  try {
    await confirmPasswordReset(auth, code, newPassword);
    
    return {
      success: true,
      message: 'Password reset successfully'
    };
  } catch (error: any) {
    console.error('Password reset confirmation error:', error);
    
    let errorMessage = 'Failed to reset password';
    
    switch (error.code) {
      case 'auth/expired-action-code':
        errorMessage = 'Password reset link has expired. Please request a new one';
        break;
      case 'auth/invalid-action-code':
        errorMessage = 'Invalid password reset link. Please request a new one';
        break;
      case 'auth/weak-password':
        errorMessage = 'Password is too weak. Please choose a stronger password';
        break;
      case 'auth/user-disabled':
        errorMessage = 'This account has been disabled';
        break;
      case 'auth/user-not-found':
        errorMessage = 'No account found for this reset request';
        break;
      default:
        errorMessage = error.message || 'Failed to reset password';
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Check action code (for email verification, password reset, etc.)
 */
export const checkActionCodeService = async (
  code: string
): Promise<PasswordResetResult & { 
  operation?: string;
  email?: string;
  previousEmail?: string;
}> => {
  try {
    const info = await checkActionCode(auth, code);
    
    return {
      success: true,
      message: 'Action code is valid',
      operation: info.operation,
      email: info.data.email || undefined,
      previousEmail: info.data.previousEmail || undefined
    };
  } catch (error: any) {
    console.error('Action code check error:', error);
    
    let errorMessage = 'Invalid or expired action code';
    
    switch (error.code) {
      case 'auth/expired-action-code':
        errorMessage = 'Action code has expired';
        break;
      case 'auth/invalid-action-code':
        errorMessage = 'Invalid action code';
        break;
      case 'auth/user-disabled':
        errorMessage = 'This account has been disabled';
        break;
      case 'auth/user-not-found':
        errorMessage = 'No account found for this action';
        break;
      default:
        errorMessage = error.message || 'Invalid or expired action code';
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Apply action code (complete email verification, password reset, etc.)
 */
export const applyActionCodeService = async (
  code: string
): Promise<PasswordResetResult> => {
  try {
    await applyActionCode(auth, code);
    
    return {
      success: true,
      message: 'Action completed successfully'
    };
  } catch (error: any) {
    console.error('Action code apply error:', error);
    
    let errorMessage = 'Failed to complete action';
    
    switch (error.code) {
      case 'auth/expired-action-code':
        errorMessage = 'Action code has expired';
        break;
      case 'auth/invalid-action-code':
        errorMessage = 'Invalid action code';
        break;
      case 'auth/user-disabled':
        errorMessage = 'This account has been disabled';
        break;
      case 'auth/user-not-found':
        errorMessage = 'No account found for this action';
        break;
      default:
        errorMessage = error.message || 'Failed to complete action';
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error.code
    };
  }
};

/**
 * Validate password strength
 */
export const validatePasswordStrength = (password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} => {
  const feedback: string[] = [];
  let score = 0;

  // Length check
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Password must be at least 8 characters long');
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one uppercase letter');
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one lowercase letter');
  }

  // Number check
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one number');
  }

  // Special character check
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include at least one special character');
  }

  return {
    isValid: score >= 3,
    score,
    feedback
  };
};

/**
 * Generate secure password suggestions
 */
export const generatePasswordSuggestions = (): string[] => {
  const adjectives = ['Secure', 'Strong', 'Safe', 'Protected', 'Guarded'];
  const nouns = ['Hotel', 'Stay', 'Room', 'Guest', 'Booking'];
  const numbers = Math.floor(Math.random() * 9999) + 1000;
  const symbols = ['!', '@', '#', '$', '%'];
  
  const suggestions = [];
  
  for (let i = 0; i < 3; i++) {
    const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const symbol = symbols[Math.floor(Math.random() * symbols.length)];
    const num = Math.floor(Math.random() * 99) + 10;
    
    suggestions.push(`${adj}${noun}${num}${symbol}`);
  }
  
  return suggestions;
};
